-- Central Database Schema for Talifouni+ Multi-tenant System

-- Enable pgcrypto extension for encryption
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Stores table
CREATE TABLE stores (
    slug VARCHAR(50) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    active BOOLEAN DEFAULT true,
    store_type VARCHAR(20) DEFAULT 'subscription' CHECK (store_type IN ('subscription', 'directory')),
    visibility_tier VARCHAR(20) DEFAULT 'free' CHECK (visibility_tier IN ('free', 'premium', 'sponsored')),
    repair_code_prefix CHAR(3) UNIQUE,
    supabase_url_encrypted BYTEA,
    supabase_key_encrypted BYTEA,
    -- Additional fields for directory stores
    description TEXT,
    website VARCHAR(255),
    services TEXT[], -- Array of services offered
    specialties TEXT[], -- Array of specialties
    hours JSONB, -- Store hours in JSON format
    rating DECIMAL(2,1) DEFAULT 0.0,
    review_count INTEGER DEFAULT 0,
    image_url VARCHAR(500),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    -- Business model fields
    featured BOOLEAN DEFAULT false,
    featured_until TIMESTAMP WITH TIME ZONE,
    monthly_clicks INTEGER DEFAULT 0,
    total_clicks INTEGER DEFAULT 0,
    last_click_reset TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Promotions table
CREATE TABLE promotions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    store_slug VARCHAR(50) REFERENCES stores(slug) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    active BOOLEAN DEFAULT true,
    valid_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Store reviews table (for directory stores)
CREATE TABLE store_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    store_slug VARCHAR(50) REFERENCES stores(slug) ON DELETE CASCADE,
    customer_name VARCHAR(255),
    customer_phone VARCHAR(20),
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    repair_type VARCHAR(100),
    verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Store photos table
CREATE TABLE store_photos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    store_slug VARCHAR(50) REFERENCES stores(slug) ON DELETE CASCADE,
    photo_url VARCHAR(500) NOT NULL,
    caption VARCHAR(255),
    is_primary BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Store analytics table
CREATE TABLE store_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    store_slug VARCHAR(50) REFERENCES stores(slug) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL, -- 'view', 'click', 'call', 'direction'
    event_date DATE NOT NULL,
    event_count INTEGER DEFAULT 1,
    metadata JSONB, -- Additional event data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(store_slug, event_type, event_date)
);

-- Visibility packages table
CREATE TABLE visibility_packages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    tier VARCHAR(20) NOT NULL CHECK (tier IN ('free', 'premium', 'sponsored')),
    price_monthly DECIMAL(10, 2),
    features JSONB, -- Package features as JSON
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Store subscriptions table (for visibility packages)
CREATE TABLE store_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    store_slug VARCHAR(50) REFERENCES stores(slug) ON DELETE CASCADE,
    package_id UUID REFERENCES visibility_packages(id),
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,
    auto_renew BOOLEAN DEFAULT true,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_stores_prefix ON stores(repair_code_prefix);
CREATE INDEX idx_stores_type ON stores(store_type);
CREATE INDEX idx_stores_visibility ON stores(visibility_tier);
CREATE INDEX idx_stores_featured ON stores(featured, featured_until);
CREATE INDEX idx_stores_location ON stores(latitude, longitude);
CREATE INDEX idx_promotions_store ON promotions(store_slug);
CREATE INDEX idx_promotions_active ON promotions(active, valid_until);
CREATE INDEX idx_reviews_store ON store_reviews(store_slug);
CREATE INDEX idx_reviews_rating ON store_reviews(rating);
CREATE INDEX idx_photos_store ON store_photos(store_slug);
CREATE INDEX idx_analytics_store_date ON store_analytics(store_slug, event_date);
CREATE INDEX idx_analytics_event_type ON store_analytics(event_type);
CREATE INDEX idx_subscriptions_store ON store_subscriptions(store_slug);
CREATE INDEX idx_subscriptions_status ON store_subscriptions(status, end_date);

-- Sample data (use your actual encryption key in ENCRYPTION_KEY env var)
-- Subscription stores (existing model)
INSERT INTO stores (slug, name, address, phone, email, store_type, visibility_tier, repair_code_prefix, supabase_url_encrypted, supabase_key_encrypted) VALUES
('techfix', 'TechFix Repair Shop', '123 Main St, City', '+1234567890', '<EMAIL>', 'subscription', 'premium', 'TFX',
 pgp_sym_encrypt('https://techfix.supabase.co', 'your-encryption-key'),
 pgp_sym_encrypt('your-techfix-key', 'your-encryption-key')),
('phonecare', 'PhoneCare Center', '456 Oak Ave, Town', '+0987654321', '<EMAIL>', 'subscription', 'free', 'PCC',
 pgp_sym_encrypt('https://phonecare.supabase.co', 'your-encryption-key'),
 pgp_sym_encrypt('your-phonecare-key', 'your-encryption-key'));

-- Directory stores (new admin-managed model)
INSERT INTO stores (slug, name, address, phone, email, store_type, visibility_tier, description, website, services, specialties, hours, rating, review_count, featured, latitude, longitude) VALUES
('mobile-doctor', 'Mobile Doctor', '789 Tech Boulevard, Tunis', '+216 71 555 0123', '<EMAIL>', 'directory', 'sponsored',
 'Professional mobile device repair with 10+ years experience. We specialize in iPhone, Samsung, and tablet repairs with genuine parts and warranty.',
 'https://mobiledoctor.tn',
 ARRAY['Screen Repair', 'Battery Replacement', 'Water Damage', 'Data Recovery', 'Software Issues'],
 ARRAY['iPhone Repair', 'Samsung Repair', 'iPad Repair', 'Quick Service'],
 '{"monday": "9:00-19:00", "tuesday": "9:00-19:00", "wednesday": "9:00-19:00", "thursday": "9:00-19:00", "friday": "9:00-19:00", "saturday": "10:00-18:00", "sunday": "closed"}'::jsonb,
 4.8, 127, true, 36.8065, 10.1815),
('fix-it-fast', 'Fix It Fast', '321 Repair Street, Sfax', '+216 74 444 0456', '<EMAIL>', 'directory', 'premium',
 'Fast and reliable phone repair service. Same-day repairs available for most common issues.',
 'https://fixitfast.tn',
 ARRAY['Screen Repair', 'Battery Replacement', 'Charging Port', 'Camera Repair'],
 ARRAY['Quick Repair', 'Same Day Service', 'Android Specialist'],
 '{"monday": "8:30-20:00", "tuesday": "8:30-20:00", "wednesday": "8:30-20:00", "thursday": "8:30-20:00", "friday": "8:30-20:00", "saturday": "9:00-17:00", "sunday": "10:00-16:00"}'::jsonb,
 4.6, 89, false, 34.7406, 10.7603),
('gadget-clinic', 'Gadget Clinic', '555 Innovation Ave, Sousse', '+216 73 333 0789', '<EMAIL>', 'directory', 'free',
 'Affordable repair solutions for all mobile devices. Student discounts available.',
 NULL,
 ARRAY['Screen Repair', 'Battery Replacement', 'Software Repair'],
 ARRAY['Budget Friendly', 'Student Discounts'],
 '{"monday": "10:00-18:00", "tuesday": "10:00-18:00", "wednesday": "10:00-18:00", "thursday": "10:00-18:00", "friday": "10:00-18:00", "saturday": "10:00-16:00", "sunday": "closed"}'::jsonb,
 4.2, 45, false, 35.8256, 10.6369);

-- RPC function to decrypt store credentials (only for subscription stores)
CREATE OR REPLACE FUNCTION get_store_credentials(store_slug TEXT)
RETURNS TABLE(supabase_url TEXT, supabase_key TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT
        pgp_sym_decrypt(supabase_url_encrypted, current_setting('app.encryption_key')) as supabase_url,
        pgp_sym_decrypt(supabase_key_encrypted, current_setting('app.encryption_key')) as supabase_key
    FROM stores
    WHERE slug = store_slug AND active = true AND store_type = 'subscription';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update store analytics
CREATE OR REPLACE FUNCTION increment_store_analytics(store_slug TEXT, event_type TEXT, metadata_json JSONB DEFAULT NULL)
RETURNS VOID AS $$
BEGIN
    INSERT INTO store_analytics (store_slug, event_type, event_date, event_count, metadata)
    VALUES (store_slug, event_type, CURRENT_DATE, 1, metadata_json)
    ON CONFLICT (store_slug, event_type, event_date)
    DO UPDATE SET
        event_count = store_analytics.event_count + 1,
        metadata = COALESCE(metadata_json, store_analytics.metadata);

    -- Update monthly clicks counter for stores
    IF event_type = 'click' THEN
        UPDATE stores
        SET
            monthly_clicks = monthly_clicks + 1,
            total_clicks = total_clicks + 1
        WHERE slug = store_slug;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Sample promotions
INSERT INTO promotions (store_slug, title, description, valid_until) VALUES
('techfix', '20% Off Screen Repairs', 'Get 20% discount on all screen repairs this month', '2024-12-31 23:59:59'),
('phonecare', 'Free Diagnostic', 'Free device diagnostic with any repair service', '2024-12-31 23:59:59'),
('mobile-doctor', 'Premium Service Package', 'Screen repair + protective glass installation for 150 TND', '2025-01-31 23:59:59'),
('fix-it-fast', 'Same Day Guarantee', 'Get your phone fixed the same day or get 50% off', '2025-02-28 23:59:59');

-- Sample visibility packages
INSERT INTO visibility_packages (name, tier, price_monthly, features) VALUES
('Free Listing', 'free', 0.00, '{"max_photos": 1, "basic_info": true, "search_results": true}'),
('Premium Listing', 'premium', 49.99, '{"max_photos": 5, "enhanced_profile": true, "priority_search": true, "analytics": true, "promotions": 2}'),
('Sponsored Placement', 'sponsored', 149.99, '{"max_photos": 10, "featured_homepage": true, "top_search": true, "advanced_analytics": true, "unlimited_promotions": true, "social_media": true}');

-- Sample store reviews
INSERT INTO store_reviews (store_slug, customer_name, customer_phone, rating, comment, repair_type, verified) VALUES
('mobile-doctor', 'Ahmed Ben Ali', '+216 92 123 456', 5, 'Excellent service! Fixed my iPhone screen in 30 minutes. Very professional.', 'Screen Repair', true),
('mobile-doctor', 'Fatma Trabelsi', '+216 98 765 432', 5, 'Great experience, honest pricing and quality work.', 'Battery Replacement', true),
('mobile-doctor', 'Mohamed Sassi', '+216 94 555 123', 4, 'Good service but a bit expensive. Quality is worth it though.', 'Water Damage', true),
('fix-it-fast', 'Leila Mansouri', '+216 97 888 999', 5, 'Super fast service as promised! Same day repair.', 'Screen Repair', true),
('fix-it-fast', 'Karim Bouazizi', '+216 91 222 333', 4, 'Professional team, reasonable prices.', 'Charging Port', true),
('gadget-clinic', 'Sarra Mejri', '+216 99 444 555', 4, 'Good value for money, especially with student discount.', 'Screen Repair', true),
('gadget-clinic', 'Youssef Khelifi', '+216 93 666 777', 4, 'Affordable and reliable service.', 'Battery Replacement', true);

-- Sample store photos
INSERT INTO store_photos (store_slug, photo_url, caption, is_primary) VALUES
('mobile-doctor', '/images/stores/mobile-doctor-front.jpg', 'Store Front', true),
('mobile-doctor', '/images/stores/mobile-doctor-workshop.jpg', 'Repair Workshop', false),
('mobile-doctor', '/images/stores/mobile-doctor-team.jpg', 'Our Expert Team', false),
('fix-it-fast', '/images/stores/fix-it-fast-exterior.jpg', 'Store Location', true),
('fix-it-fast', '/images/stores/fix-it-fast-interior.jpg', 'Service Counter', false),
('gadget-clinic', '/images/stores/gadget-clinic-shop.jpg', 'Shop Interior', true);

-- Sample store subscriptions
INSERT INTO store_subscriptions (store_slug, package_id, start_date, end_date, status) VALUES
('mobile-doctor', (SELECT id FROM visibility_packages WHERE tier = 'sponsored'), '2024-12-01', '2025-12-01', 'active'),
('fix-it-fast', (SELECT id FROM visibility_packages WHERE tier = 'premium'), '2024-11-15', '2025-11-15', 'active'),
('gadget-clinic', (SELECT id FROM visibility_packages WHERE tier = 'free'), '2024-10-01', NULL, 'active');