-- Central Database Schema for Talifouni+ Multi-tenant System

-- Enable pgcrypto extension for encryption
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Simplified stores table for v1
CREATE TABLE stores (
    slug VARCHAR(50) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    active BOOLEAN DEFAULT true,
    store_type VARCHAR(20) DEFAULT 'directory' CHECK (store_type IN ('subscription', 'directory')),
    visibility_tier VARCHAR(20) DEFAULT 'free' CHECK (visibility_tier IN ('free', 'premium', 'sponsored')),
    repair_code_prefix CHAR(3) UNIQUE,
    supabase_url_encrypted BYTEA,
    supabase_key_encrypted BYTEA,
    -- Essential directory store fields
    description TEXT,
    services TEXT[], -- Array of services offered
    specialties TEXT[], -- Array of specialties
    hours VARCHAR(255), -- Simple hours string
    rating DECIMAL(2,1) DEFAULT 0.0,
    review_count INTEGER DEFAULT 0,
    image_url VARCHAR(500),
    -- Simple business model fields
    featured BOOLEAN DEFAULT false,
    total_clicks INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Promotions table
CREATE TABLE promotions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    store_slug VARCHAR(50) REFERENCES stores(slug) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    active BOOLEAN DEFAULT true,
    valid_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Simple store reviews table (anonymous reviews)
CREATE TABLE store_reviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    store_slug VARCHAR(50) REFERENCES stores(slug) ON DELETE CASCADE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5) NOT NULL,
    comment TEXT,
    repair_type VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Simple repair tracking table (for directory stores without their own system)
CREATE TABLE simple_repairs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    store_slug VARCHAR(50) REFERENCES stores(slug) ON DELETE CASCADE,
    customer_phone VARCHAR(20) NOT NULL,
    device_model VARCHAR(255),
    issue_description TEXT,
    status VARCHAR(20) DEFAULT 'received' CHECK (status IN ('received', 'diagnosis', 'in_progress', 'completed', 'ready_for_pickup')),
    estimated_cost DECIMAL(10, 2),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_stores_prefix ON stores(repair_code_prefix);
CREATE INDEX idx_stores_type ON stores(store_type);
CREATE INDEX idx_stores_visibility ON stores(visibility_tier);
CREATE INDEX idx_stores_featured ON stores(featured);
CREATE INDEX idx_promotions_store ON promotions(store_slug);
CREATE INDEX idx_promotions_active ON promotions(active, valid_until);
CREATE INDEX idx_reviews_store ON store_reviews(store_slug);
CREATE INDEX idx_reviews_rating ON store_reviews(rating);
CREATE INDEX idx_simple_repairs_store ON simple_repairs(store_slug);
CREATE INDEX idx_simple_repairs_phone ON simple_repairs(customer_phone);

-- Sample data for v1 (simplified)
-- Directory stores (main focus for v1)
INSERT INTO stores (slug, name, address, phone, email, store_type, visibility_tier, description, services, specialties, hours, rating, review_count, featured) VALUES
('mobile-doctor', 'Mobile Doctor', '789 Tech Boulevard, Tunis', '+216 71 555 0123', '<EMAIL>', 'directory', 'sponsored',
 'Professional mobile device repair with 10+ years experience. We specialize in iPhone, Samsung, and tablet repairs.',
 ARRAY['Screen Repair', 'Battery Replacement', 'Water Damage', 'Data Recovery'],
 ARRAY['iPhone Repair', 'Samsung Repair', 'Quick Service'],
 'Mon-Fri: 9:00-19:00, Sat: 10:00-18:00, Sun: Closed',
 4.8, 127, true),
('fix-it-fast', 'Fix It Fast', '321 Repair Street, Sfax', '+216 74 444 0456', '<EMAIL>', 'directory', 'premium',
 'Fast and reliable phone repair service. Same-day repairs available for most common issues.',
 ARRAY['Screen Repair', 'Battery Replacement', 'Charging Port', 'Camera Repair'],
 ARRAY['Quick Repair', 'Same Day Service', 'Android Specialist'],
 'Mon-Fri: 8:30-20:00, Sat: 9:00-17:00, Sun: 10:00-16:00',
 4.6, 89, false),
('gadget-clinic', 'Gadget Clinic', '555 Innovation Ave, Sousse', '+216 73 333 0789', '<EMAIL>', 'directory', 'free',
 'Affordable repair solutions for all mobile devices. Student discounts available.',
 ARRAY['Screen Repair', 'Battery Replacement', 'Software Repair'],
 ARRAY['Budget Friendly', 'Student Discounts'],
 'Mon-Fri: 10:00-18:00, Sat: 10:00-16:00, Sun: Closed',
 4.2, 45, false);

-- One subscription store example
INSERT INTO stores (slug, name, address, phone, email, store_type, visibility_tier, repair_code_prefix, supabase_url_encrypted, supabase_key_encrypted, description, rating, review_count) VALUES
('techfix', 'TechFix Center', '123 Avenue Habib Bourguiba, Tunis', '+216 71 123 456', '<EMAIL>', 'subscription', 'premium', 'TFX',
 pgp_sym_encrypt('https://techfix.supabase.co', 'your-encryption-key'),
 pgp_sym_encrypt('your-techfix-key', 'your-encryption-key'),
 'Full-service repair center with advanced repair tracking system.',
 4.8, 156);

-- RPC function to decrypt store credentials (only for subscription stores)
CREATE OR REPLACE FUNCTION get_store_credentials(store_slug TEXT)
RETURNS TABLE(supabase_url TEXT, supabase_key TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT
        pgp_sym_decrypt(supabase_url_encrypted, current_setting('app.encryption_key')) as supabase_url,
        pgp_sym_decrypt(supabase_key_encrypted, current_setting('app.encryption_key')) as supabase_key
    FROM stores
    WHERE slug = store_slug AND active = true AND store_type = 'subscription';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Simple function to update store rating when new review is added
CREATE OR REPLACE FUNCTION update_store_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE stores
    SET
        rating = (SELECT AVG(rating)::DECIMAL(2,1) FROM store_reviews WHERE store_slug = NEW.store_slug),
        review_count = (SELECT COUNT(*) FROM store_reviews WHERE store_slug = NEW.store_slug)
    WHERE slug = NEW.store_slug;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update store rating
CREATE TRIGGER update_store_rating_trigger
    AFTER INSERT ON store_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_store_rating();

-- Sample promotions
INSERT INTO promotions (store_slug, title, description, valid_until) VALUES
('techfix', '20% Off Screen Repairs', 'Get 20% discount on all screen repairs this month', '2025-01-31 23:59:59'),
('mobile-doctor', 'Premium Service Package', 'Screen repair + protective glass installation for 150 TND', '2025-01-31 23:59:59'),
('fix-it-fast', 'Same Day Guarantee', 'Get your phone fixed the same day or get 50% off', '2025-02-28 23:59:59'),
('gadget-clinic', 'Student Discount', '15% off for students with valid ID', '2025-06-30 23:59:59');

-- Sample store reviews (anonymous for v1)
INSERT INTO store_reviews (store_slug, rating, comment, repair_type) VALUES
('mobile-doctor', 5, 'Excellent service! Fixed my iPhone screen in 30 minutes. Very professional.', 'Screen Repair'),
('mobile-doctor', 5, 'Great experience, honest pricing and quality work.', 'Battery Replacement'),
('mobile-doctor', 4, 'Good service but a bit expensive. Quality is worth it though.', 'Water Damage'),
('fix-it-fast', 5, 'Super fast service as promised! Same day repair.', 'Screen Repair'),
('fix-it-fast', 4, 'Professional team, reasonable prices.', 'Charging Port'),
('gadget-clinic', 4, 'Good value for money, especially with student discount.', 'Screen Repair'),
('gadget-clinic', 4, 'Affordable and reliable service.', 'Battery Replacement'),
('techfix', 5, 'Amazing repair tracking system. Always know the status of my device.', 'Screen Repair'),
('techfix', 4, 'Professional service with detailed updates.', 'Battery Replacement');

-- Sample simple repairs (for directory stores)
INSERT INTO simple_repairs (store_slug, customer_phone, device_model, issue_description, status, estimated_cost, notes) VALUES
('mobile-doctor', '+216 92 123 456', 'iPhone 14 Pro', 'Cracked screen', 'in_progress', 180.00, 'Waiting for genuine Apple screen to arrive'),
('fix-it-fast', '+216 97 888 999', 'Samsung Galaxy S23', 'Battery draining fast', 'diagnosis', 120.00, 'Running battery diagnostics'),
('gadget-clinic', '+216 99 444 555', 'iPhone 12', 'Water damage', 'received', 200.00, 'Initial assessment needed');