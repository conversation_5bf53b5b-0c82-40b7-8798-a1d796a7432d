-- Central Database Schema for Talifouni+ Multi-tenant System

-- Enable pgcrypto extension for encryption
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Stores table
CREATE TABLE stores (
    slug VARCHAR(50) PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    active BOOLEAN DEFAULT true,
    repair_code_prefix CHAR(3) UNIQUE NOT NULL,
    supabase_url_encrypted BYTEA NOT NULL,
    supabase_key_encrypted BYTEA NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Promotions table
CREATE TABLE promotions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    store_slug VARCHAR(50) REFERENCES stores(slug) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    active BOOLEAN DEFAULT true,
    valid_until TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_stores_prefix ON stores(repair_code_prefix);
CREATE INDEX idx_promotions_store ON promotions(store_slug);
CREATE INDEX idx_promotions_active ON promotions(active, valid_until);

-- Sample data (use your actual encryption key in ENCRYPTION_KEY env var)
INSERT INTO stores (slug, name, address, phone, email, repair_code_prefix, supabase_url_encrypted, supabase_key_encrypted) VALUES
('techfix', 'TechFix Repair Shop', '123 Main St, City', '+1234567890', '<EMAIL>', 'TFX', 
 pgp_sym_encrypt('https://techfix.supabase.co', 'your-encryption-key'), 
 pgp_sym_encrypt('your-techfix-key', 'your-encryption-key')),
('phonecare', 'PhoneCare Center', '456 Oak Ave, Town', '+0987654321', '<EMAIL>', 'PCC', 
 pgp_sym_encrypt('https://phonecare.supabase.co', 'your-encryption-key'), 
 pgp_sym_encrypt('your-phonecare-key', 'your-encryption-key'));

-- RPC function to decrypt store credentials
CREATE OR REPLACE FUNCTION get_store_credentials(store_slug TEXT)
RETURNS TABLE(supabase_url TEXT, supabase_key TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pgp_sym_decrypt(supabase_url_encrypted, current_setting('app.encryption_key')) as supabase_url,
        pgp_sym_decrypt(supabase_key_encrypted, current_setting('app.encryption_key')) as supabase_key
    FROM stores 
    WHERE slug = store_slug AND active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

INSERT INTO promotions (store_slug, title, description, valid_until) VALUES
('techfix', '20% Off Screen Repairs', 'Get 20% discount on all screen repairs this month', '2024-12-31 23:59:59'),
('phonecare', 'Free Diagnostic', 'Free device diagnostic with any repair service', '2024-12-31 23:59:59');