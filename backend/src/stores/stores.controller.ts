import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  HttpException,
  HttpStatus,
} from "@nestjs/common";
import {
  StoresService,
  CreateStoreDto,
  UpdateStoreDto,
} from "./stores.service";

@Controller("stores")
export class StoresController {
  constructor(private readonly storesService: StoresService) {}

  @Get()
  async getStores(@Query("includeInactive") includeInactive?: string) {
    try {
      const stores = await this.storesService.getAllStores(
        includeInactive === "true"
      );
      return stores;
    } catch (error) {
      throw new HttpException(
        error.message || "Failed to fetch stores",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(":slug")
  async getStore(@Param("slug") slug: string) {
    try {
      const store = await this.storesService.getStoreBySlug(slug);
      return store;
    } catch (error) {
      throw new HttpException(
        error.message || "Store not found",
        HttpStatus.NOT_FOUND
      );
    }
  }

  @Post()
  async createStore(@Body() createStoreDto: CreateStoreDto) {
    try {
      const store = await this.storesService.createStore(createStoreDto);
      return store;
    } catch (error) {
      throw new HttpException(
        error.message || "Failed to create store",
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Put(":slug")
  async updateStore(
    @Param("slug") slug: string,
    @Body() updateStoreDto: UpdateStoreDto
  ) {
    try {
      const store = await this.storesService.updateStore(slug, updateStoreDto);
      return store;
    } catch (error) {
      throw new HttpException(
        error.message || "Failed to update store",
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Delete(":slug")
  async deleteStore(@Param("slug") slug: string) {
    try {
      const result = await this.storesService.deleteStore(slug);
      return result;
    } catch (error) {
      throw new HttpException(
        error.message || "Failed to delete store",
        HttpStatus.BAD_REQUEST
      );
    }
  }

  @Post(":slug/analytics/:eventType")
  async trackEvent(
    @Param("slug") slug: string,
    @Param("eventType") eventType: string,
    @Body() metadata?: any
  ) {
    try {
      await this.storesService.incrementAnalytics(slug, eventType, metadata);
      return { success: true };
    } catch (error) {
      // Analytics failures shouldn't break the user experience
      console.error("Analytics tracking failed:", error);
      return { success: false, error: error.message };
    }
  }

  @Get(":slug/analytics")
  async getStoreAnalytics(
    @Param("slug") slug: string,
    @Query("startDate") startDate?: string,
    @Query("endDate") endDate?: string
  ) {
    try {
      const analytics = await this.storesService.getStoreAnalytics(
        slug,
        startDate,
        endDate
      );
      return { analytics };
    } catch (error) {
      throw new HttpException(
        error.message || "Failed to fetch analytics",
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
