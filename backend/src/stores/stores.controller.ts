import { Controller, Get, HttpException, HttpStatus } from '@nestjs/common';
import { StoresService } from './stores.service';

@Controller('stores')
export class StoresController {
  constructor(private readonly storesService: StoresService) {}

  @Get()
  async getStores() {
    try {
      const stores = await this.storesService.getAllStores();
      return stores;
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to fetch stores',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}