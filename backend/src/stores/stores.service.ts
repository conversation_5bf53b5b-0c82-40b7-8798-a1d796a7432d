import { Injectable } from "@nestjs/common";
import { SupabaseService } from "../supabase/supabase.service";

export interface CreateStoreDto {
  slug: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  storeType: "subscription" | "directory";
  visibilityTier?: "free" | "premium" | "sponsored";
  description?: string;
  website?: string;
  services?: string[];
  specialties?: string[];
  hours?: any;
  latitude?: number;
  longitude?: number;
  repairCodePrefix?: string;
  supabaseUrl?: string;
  supabaseKey?: string;
}

export interface UpdateStoreDto {
  name?: string;
  address?: string;
  phone?: string;
  email?: string;
  visibilityTier?: "free" | "premium" | "sponsored";
  description?: string;
  website?: string;
  services?: string[];
  specialties?: string[];
  hours?: any;
  featured?: boolean;
  featuredUntil?: string;
  active?: boolean;
}

@Injectable()
export class StoresService {
  constructor(private readonly supabaseService: SupabaseService) {}

  async getAllStores(includeInactive = false) {
    const centralClient = this.supabaseService.getCentralClient();

    let query = centralClient.from("stores").select(`
        slug, name, address, phone, email, active, store_type, visibility_tier,
        description, website, services, specialties, hours, rating, review_count,
        featured, featured_until, latitude, longitude, total_clicks, created_at
      `);

    if (!includeInactive) {
      query = query.eq("active", true);
    }

    // Order by visibility tier and featured status for better UX
    query = query
      .order("visibility_tier", { ascending: false })
      .order("featured", { ascending: false })
      .order("rating", { ascending: false });

    const { data, error } = await query;

    if (error) throw new Error(error.message);

    return {
      stores: data || [],
    };
  }

  async getStoreBySlug(slug: string) {
    const centralClient = this.supabaseService.getCentralClient();

    const { data, error } = await centralClient
      .from("stores")
      .select(
        `
        *,
        promotions(*),
        store_reviews(*),
        store_photos(*)
      `
      )
      .eq("slug", slug)
      .eq("active", true)
      .single();

    if (error) throw new Error(error.message);
    if (!data) throw new Error("Store not found");

    // Increment view analytics
    await this.incrementAnalytics(slug, "view");

    return data;
  }

  async createStore(createStoreDto: CreateStoreDto) {
    const centralClient = this.supabaseService.getCentralClient();

    const storeData: any = {
      slug: createStoreDto.slug,
      name: createStoreDto.name,
      address: createStoreDto.address,
      phone: createStoreDto.phone,
      email: createStoreDto.email,
      store_type: createStoreDto.storeType,
      visibility_tier: createStoreDto.visibilityTier || "free",
      description: createStoreDto.description,
      website: createStoreDto.website,
      services: createStoreDto.services,
      specialties: createStoreDto.specialties,
      hours: createStoreDto.hours,
      latitude: createStoreDto.latitude,
      longitude: createStoreDto.longitude,
    };

    // Handle subscription stores
    if (createStoreDto.storeType === "subscription") {
      if (
        !createStoreDto.repairCodePrefix ||
        !createStoreDto.supabaseUrl ||
        !createStoreDto.supabaseKey
      ) {
        throw new Error(
          "Subscription stores require repair code prefix and Supabase credentials"
        );
      }

      storeData.repair_code_prefix = createStoreDto.repairCodePrefix;
      // Encrypt credentials (you'll need to implement encryption)
      storeData.supabase_url_encrypted = createStoreDto.supabaseUrl; // TODO: Encrypt
      storeData.supabase_key_encrypted = createStoreDto.supabaseKey; // TODO: Encrypt
    }

    const { data, error } = await centralClient
      .from("stores")
      .insert(storeData)
      .select()
      .single();

    if (error) throw new Error(error.message);

    return data;
  }

  async updateStore(slug: string, updateStoreDto: UpdateStoreDto) {
    const centralClient = this.supabaseService.getCentralClient();

    const { data, error } = await centralClient
      .from("stores")
      .update(updateStoreDto)
      .eq("slug", slug)
      .select()
      .single();

    if (error) throw new Error(error.message);
    if (!data) throw new Error("Store not found");

    return data;
  }

  async deleteStore(slug: string) {
    const centralClient = this.supabaseService.getCentralClient();

    const { error } = await centralClient
      .from("stores")
      .delete()
      .eq("slug", slug);

    if (error) throw new Error(error.message);

    return { success: true };
  }

  async incrementAnalytics(
    storeSlug: string,
    eventType: string,
    metadata?: any
  ) {
    const centralClient = this.supabaseService.getCentralClient();

    const { error } = await centralClient.rpc("increment_store_analytics", {
      store_slug: storeSlug,
      event_type: eventType,
      metadata_json: metadata,
    });

    if (error) {
      console.error("Analytics error:", error);
      // Don't throw error for analytics failures
    }
  }

  async getStoreAnalytics(
    storeSlug: string,
    startDate?: string,
    endDate?: string
  ) {
    const centralClient = this.supabaseService.getCentralClient();

    let query = centralClient
      .from("store_analytics")
      .select("*")
      .eq("store_slug", storeSlug);

    if (startDate) {
      query = query.gte("event_date", startDate);
    }
    if (endDate) {
      query = query.lte("event_date", endDate);
    }

    const { data, error } = await query.order("event_date", {
      ascending: false,
    });

    if (error) throw new Error(error.message);

    return data || [];
  }
}
