import { Injectable } from '@nestjs/common';
import { SupabaseService } from '../supabase/supabase.service';

@Injectable()
export class StoresService {
  constructor(private readonly supabaseService: SupabaseService) {}

  async getAllStores() {
    const centralClient = this.supabaseService.getCentralClient();
    
    const { data, error } = await centralClient
      .from('stores')
      .select('slug, name, address, phone, email, active')
      .eq('active', true);

    if (error) throw new Error(error.message);

    return {
      stores: data || []
    };
  }
}