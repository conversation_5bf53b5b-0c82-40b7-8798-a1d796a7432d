import { Injectable } from '@nestjs/common';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

@Injectable()
export class SupabaseService {
  private centralClient: SupabaseClient;
  private storeClients: Map<string, SupabaseClient> = new Map();

  constructor() {
    this.centralClient = createClient(
      process.env.CENTRAL_SUPABASE_URL!,
      process.env.CENTRAL_SUPABASE_ANON_KEY!
    );
  }

  getCentralClient(): SupabaseClient {
    return this.centralClient;
  }

  async getStoreClient(storeSlug: string): Promise<SupabaseClient> {
    if (!this.storeClients.has(storeSlug)) {
      const { data } = await this.centralClient
        .rpc('get_store_credentials', { store_slug: storeSlug });
      
      if (!data) {
        throw new Error(`Store not found: ${storeSlug}`);
      }

      this.storeClients.set(storeSlug, createClient(data.supabase_url, data.supabase_key));
    }

    return this.storeClients.get(storeSlug)!;
  }

  async findStoreByRepairCode(repairCode: string): Promise<string | null> {
    const prefix = repairCode.substring(0, 3).toUpperCase();
    
    const { data } = await this.centralClient
      .from('stores')
      .select('slug')
      .eq('repair_code_prefix', prefix)
      .eq('active', true)
      .single();

    return data?.slug || null;
  }
}