import { Injectable } from '@nestjs/common';
import { SupabaseService } from '../supabase/supabase.service';

@Injectable()
export class RepairService {
  constructor(private readonly supabaseService: SupabaseService) {}

  async findRepair(repairCode: string) {
    const storeSlug = await this.supabaseService.findStoreByRepairCode(repairCode);
    
    if (!storeSlug) {
      throw new Error('Store not found for repair code');
    }

    const storeClient = await this.supabaseService.getStoreClient(storeSlug);
    
    const { data, error } = await storeClient
      .from('repairs')
      .select('*')
      .eq('ticket_number', parseInt(repairCode.substring(3)))
      .single();

    if (error) throw new Error(error.message);
    if (!data) throw new Error('Repair not found');

    return {
      store: storeSlug,
      repair: data
    };
  }
}