import { Injectable } from "@nestjs/common";
import { SupabaseService } from "../supabase/supabase.service";

@Injectable()
export class RepairService {
  constructor(private readonly supabaseService: SupabaseService) {}

  async findRepair(repairCode: string) {
    const centralClient = this.supabaseService.getCentralClient();
    const prefix = repairCode.substring(0, 3).toUpperCase();
    const ticketNumber = repairCode.substring(3);

    // First, find the store by repair code prefix
    const { data: store } = await centralClient
      .from("stores")
      .select("slug, name, store_type")
      .eq("repair_code_prefix", prefix)
      .eq("active", true)
      .single();

    if (!store) {
      throw new Error("Store not found for repair code");
    }

    // Handle different store types
    if (store.store_type === "subscription") {
      // For subscription stores, query their own Supabase instance
      const storeClient = await this.supabaseService.getStoreClient(store.slug);

      const { data, error } = await storeClient
        .from("repairs")
        .select("*")
        .eq("ticket_number", parseInt(ticketNumber))
        .single();

      if (error) throw new Error(error.message);
      if (!data) throw new Error("Repair not found");

      return {
        store: store.slug,
        store_name: store.name,
        repair: data,
      };
    } else {
      // For directory stores, query the central simple_repairs table
      const { data, error } = await centralClient
        .from("simple_repairs")
        .select("*")
        .eq("store_slug", store.slug)
        .eq("id", ticketNumber) // Assuming ticket number is the repair ID
        .single();

      if (error) throw new Error(error.message);
      if (!data) throw new Error("Repair not found");

      return {
        store: store.slug,
        store_name: store.name,
        repair: data,
      };
    }
  }
}
