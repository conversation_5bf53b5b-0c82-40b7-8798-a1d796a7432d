import { Controller, Post, Body, HttpException, HttpStatus } from '@nestjs/common';
import { RepairService } from './repair.service';

@Controller('track-repair')
export class RepairController {
  constructor(private readonly repairService: RepairService) {}

  @Post()
  async trackRepair(@Body() body: { repairCode: string }) {
    try {
      const repair = await this.repairService.findRepair(body.repairCode);
      return repair;
    } catch (error) {
      throw new HttpException(
        error.message || 'Repair not found',
        HttpStatus.NOT_FOUND
      );
    }
  }
}