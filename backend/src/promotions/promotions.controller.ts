import { Controller, Get, Param, HttpException, HttpStatus } from '@nestjs/common';
import { PromotionsService } from './promotions.service';

@Controller('promos')
export class PromotionsController {
  constructor(private readonly promotionsService: PromotionsService) {}

  @Get(':store_slug')
  async getPromotions(@Param('store_slug') storeSlug: string) {
    try {
      const promotions = await this.promotionsService.getStorePromotions(storeSlug);
      return promotions;
    } catch (error) {
      throw new HttpException(
        error.message || 'Promotions not found',
        HttpStatus.NOT_FOUND
      );
    }
  }
}