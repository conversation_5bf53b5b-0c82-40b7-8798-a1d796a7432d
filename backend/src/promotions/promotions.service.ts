import { Injectable } from '@nestjs/common';
import { SupabaseService } from '../supabase/supabase.service';

@Injectable()
export class PromotionsService {
  constructor(private readonly supabaseService: SupabaseService) {}

  async getStorePromotions(storeSlug: string) {
    const centralClient = this.supabaseService.getCentralClient();
    
    const { data, error } = await centralClient
      .from('promotions')
      .select('*')
      .eq('store_slug', storeSlug)
      .eq('active', true)
      .gte('valid_until', new Date().toISOString());

    if (error) throw new Error(error.message);

    return {
      store: storeSlug,
      promotions: data || []
    };
  }
}