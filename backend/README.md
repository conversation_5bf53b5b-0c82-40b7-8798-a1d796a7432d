# Talifouni+ Gateway API

Multi-tenant NestJS Gateway API for the Talifouni+ PWA repair tracking system.

## Setup

1. Install dependencies:
```bash
npm install
```

2. Copy environment variables:
```bash
cp .env.example .env
```

3. Configure your environment variables in `.env`

4. Start development server:
```bash
npm run start:dev
```

## API Endpoints

### Repair Tracking
- `POST /track-repair` - Track repair by repair code
  ```json
  {
    "repairCode": "ABC123456"
  }
  ```

### Promotions
- `GET /promos/:store_slug` - Get active promotions for a store

### Stores
- `GET /stores` - Get list of all active stores

## Database Schema

### Central Database Tables

#### stores
- `slug` (string, primary key)
- `name` (string)
- `address` (string)
- `phone` (string)
- `email` (string)
- `active` (boolean)
- `repair_code_prefix` (string, 3 chars)

#### promotions
- `id` (uuid, primary key)
- `store_slug` (string, foreign key)
- `title` (string)
- `description` (text)
- `active` (boolean)
- `valid_until` (timestamp)

#### store_phone_mappings
- `phone_number` (string, primary key)
- `store_slug` (string, foreign key)

### Store-specific Database Tables

#### repairs
- `id` (uuid, primary key)
- `ticket_number` (integer, unique)
- `customer_name` (text)
- `customer_phone` (text)
- `phone_model` (text)
- `problem_description` (text)
- `status` (text)
- `payment_status` (text)
- `repair_price` (numeric)
- `down_payment` (numeric)
- `created_at` (timestamp)
- `updated_at` (timestamp)