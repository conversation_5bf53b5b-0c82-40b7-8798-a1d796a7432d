@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Tech Blue & White Theme */
    --background: 0 0% 100%;
    --foreground: 222 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 84% 4.9%;

    /* Primary: Tech Blue */
    --primary: 214 95% 50%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 214 95% 60%;
    --primary-dark: 214 95% 40%;

    /* Secondary: Light Blue */
    --secondary: 214 32% 95%;
    --secondary-foreground: 214 95% 50%;

    /* Muted: Subtle Grays */
    --muted: 220 13% 96%;
    --muted-foreground: 215 16% 47%;

    /* Accent: Bright Blue */
    --accent: 214 95% 55%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 214 95% 50%;

    /* Success Color */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;

    /* Warning Color */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;

    /* App Specific Colors */
    --status-pending: 38 92% 50%;
    --status-progress: 214 95% 50%;
    --status-completed: 142 76% 36%;
    --promo-bg: 214 32% 95%;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-light)));
    --gradient-secondary: linear-gradient(135deg, hsl(var(--secondary)), hsl(var(--muted)));
    
    /* Shadows */
    --shadow-card: 0 2px 8px -2px hsl(var(--primary) / 0.1);
    --shadow-button: 0 4px 12px -2px hsl(var(--primary) / 0.2);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Hide scrollbars but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Mobile-first optimizations */
  html {
    scroll-behavior: smooth;
    -webkit-tap-highlight-color: transparent;
  }

  /* Touch-friendly buttons */
  button, 
  [role="button"] {
    touch-action: manipulation;
  }

  /* PWA safe areas */
  body {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }
}