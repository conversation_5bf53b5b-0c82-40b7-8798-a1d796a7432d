import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AppProvider } from "@/contexts/AppContext";
import Index from "./pages/Index";
import StoreDetails from "./pages/StoreDetails";
import StoreList from "./pages/StoreList";
import RepairHistory from "./pages/RepairHistory";
import Support from "./pages/Support";
import Profile from "./pages/Profile";
import AdminDashboard from "./pages/AdminDashboard";
import AdminStoreForm from "./pages/AdminStoreForm";
import AdminRepairs from "./pages/AdminRepairs";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AppProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/store/:id" element={<StoreDetails />} />
            <Route path="/stores" element={<StoreList />} />
            <Route path="/history" element={<RepairHistory />} />
            <Route path="/support" element={<Support />} />
            <Route path="/profile" element={<Profile />} />
            {/* Admin Routes */}
            <Route path="/admin" element={<AdminDashboard />} />
            <Route path="/admin/stores/new" element={<AdminStoreForm />} />
            <Route
              path="/admin/stores/:slug/edit"
              element={<AdminStoreForm />}
            />
            <Route path="/admin/repairs" element={<AdminRepairs />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AppProvider>
  </QueryClientProvider>
);

export default App;
