import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Search, Smartphone, Clock, CheckCircle, Wrench } from "lucide-react";
import { useApp } from "@/contexts/AppContext";
import { RepairStatus } from "@/data/mockData";
import { toast } from "@/hooks/use-toast";

export const RepairTracker = () => {
  const [searchValue, setSearchValue] = useState("");
  const [showStatus, setShowStatus] = useState(false);
  const [foundRepair, setFoundRepair] = useState<RepairStatus | null>(null);
  const [searchParams] = useSearchParams();
  const { findRepair } = useApp();

  useEffect(() => {
    const trackParam = searchParams.get('track');
    if (trackParam) {
      setSearchValue(trackParam);
      handleTrack(trackParam);
    }
  }, [searchParams]);

  const handleTrack = (code?: string) => {
    const searchCode = code || searchValue.trim();
    if (searchCode) {
      // Try to find repair by code first, then by phone number
      let repair = findRepair('', searchCode);
      if (!repair) {
        repair = findRepair(searchCode);
      }
      
      if (repair) {
        setFoundRepair(repair);
        setShowStatus(true);
        toast({
          title: "Repair found!",
          description: `${repair.deviceModel} - ${repair.issueType}`,
        });
      } else {
        toast({
          title: "Repair not found",
          description: "Please check your phone number or repair code",
          variant: "destructive"
        });
        setShowStatus(false);
        setFoundRepair(null);
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending": return "bg-status-pending";
      case "progress": return "bg-status-progress";
      case "completed": return "bg-status-completed";
      default: return "bg-muted";
    }
  };

  const getStepIcon = (step: any) => {
    if (step.completed) return <CheckCircle className="w-4 h-4 text-success" />;
    if (step.current) return <Wrench className="w-4 h-4 text-primary animate-pulse" />;
    return <Clock className="w-4 h-4 text-muted-foreground" />;
  };

  return (
    <div id="repair-tracker" className="space-y-6">
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-2 text-primary">
          <Smartphone className="w-8 h-8" />
          <h2 className="text-2xl font-bold">Track Your Repair</h2>
        </div>
        
        <div className="space-y-3">
          <Input
            placeholder="Enter phone number or repair code"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            className="text-center"
            onKeyPress={(e) => e.key === 'Enter' && handleTrack()}
          />
          <Button 
            onClick={() => handleTrack()}
            variant="track"
            size="lg"
            className="w-full"
          >
            <Search className="w-4 h-4" />
            Track Repair
          </Button>
        </div>
      </div>

      {showStatus && foundRepair && (
        <Card className="p-6 shadow-card animate-scale-in">
          <div className="space-y-6">
            {/* Header */}
            <div className="text-center space-y-2">
              <Badge className={`${getStatusColor(foundRepair.status)} text-white px-3 py-1`}>
                {foundRepair.status === "pending" && "Pending"}
                {foundRepair.status === "in-progress" && "In Progress"}
                {foundRepair.status === "diagnosis" && "Under Diagnosis"}
                {foundRepair.status === "completed" && "Completed"}
                {foundRepair.status === "ready" && "Ready for Pickup"}
              </Badge>
              <h3 className="text-lg font-semibold">{foundRepair.deviceModel}</h3>
              <p className="text-sm text-muted-foreground">
                Repair ID: {foundRepair.repairCode} • {foundRepair.storeName}
              </p>
            </div>

            {/* Progress Steps */}
            <div className="space-y-4">
              <h4 className="font-medium">Repair Progress</h4>
              <div className="space-y-3">
                {foundRepair.progress.map((step, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    {getStepIcon(step)}
                    <div className="flex-1">
                      <p className={`text-sm ${step.completed ? 'text-foreground font-medium' : 'text-muted-foreground'}`}>
                        {step.step}
                      </p>
                      {step.date && (
                        <p className="text-xs text-muted-foreground">{step.date}</p>
                      )}
                      {step.notes && (
                        <p className="text-xs text-muted-foreground italic">{step.notes}</p>
                      )}
                    </div>
                    {step.completed && (
                      <Badge variant="secondary" className="text-xs">
                        Done
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Cost */}
            {foundRepair.cost && (
              <div className="bg-green-50 p-4 rounded-lg text-center border border-green-200">
                <p className="text-sm text-muted-foreground">Repair Cost</p>
                <p className="font-semibold text-green-700">{foundRepair.cost} TND</p>
              </div>
            )}

            {/* Estimated Time */}
            <div className="bg-promo-bg p-4 rounded-lg text-center">
              <p className="text-sm text-muted-foreground">Estimated completion</p>
              <p className="font-semibold text-primary">{foundRepair.estimatedCompletion}</p>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};