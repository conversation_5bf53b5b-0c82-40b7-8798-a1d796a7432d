import { useNavigate } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MapPin, Star, Phone, Clock, Tag } from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface Store {
  id: string;
  slug?: string;
  name: string;
  image: string;
  rating: number;
  distance: string;
  address: string;
  phone: string;
  hours: string;
  specialties: string[];
  promotion?: {
    title: string;
    discount: string;
    validUntil: string;
  };
  featured?: boolean;
  storeType?: "subscription" | "directory";
  visibilityTier?: "free" | "premium" | "sponsored";
  website?: string;
  totalClicks?: number;
}

interface StoreCardProps {
  store: Store;
  compact?: boolean;
}

export const StoreCard = ({ store, compact = false }: StoreCardProps) => {
  const navigate = useNavigate();

  const handleViewStore = async () => {
    // Track click analytics
    if (store.slug) {
      try {
        await fetch(`/api/stores/${store.slug}/analytics/click`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ source: "store_card" }),
        });
      } catch (error) {
        console.error("Failed to track click analytics:", error);
      }
    }

    navigate(`/store/${store.slug || store.id}`);
  };

  const handleCall = async () => {
    // Track call analytics
    if (store.slug) {
      try {
        await fetch(`/api/stores/${store.slug}/analytics/call`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ phone: store.phone }),
        });
      } catch (error) {
        console.error("Failed to track call analytics:", error);
      }
    }

    toast({
      title: "Calling store",
      description: `Calling ${store.name} at ${store.phone}`,
    });
  };

  const handleUseDiscount = () => {
    toast({
      title: "Discount claimed!",
      description: `${store.promotion?.title} - Show this message at the store`,
    });
  };

  const getVisibilityBadge = () => {
    if (!store.visibilityTier || store.visibilityTier === "free") return null;

    const badgeConfig = {
      premium: { label: "Premium", variant: "default" as const },
      sponsored: { label: "Sponsored", variant: "destructive" as const },
    };

    const config = badgeConfig[store.visibilityTier];
    if (!config) return null;

    return (
      <Badge variant={config.variant} className="absolute top-2 left-2 z-10">
        {config.label}
      </Badge>
    );
  };

  return (
    <Card
      className={`bg-white shadow-card hover:shadow-lg transition-all duration-200 overflow-hidden ${
        compact ? "w-80 flex-shrink-0" : "w-full"
      } ${store.featured ? "ring-2 ring-primary/20" : ""}`}
    >
      {/* Store Image */}
      <div className="relative">
        <img
          src={store.image}
          alt={store.name}
          className="w-full h-32 object-cover"
        />
        {getVisibilityBadge()}
        {store.featured && (
          <Badge className="absolute top-2 right-2 bg-primary text-primary-foreground z-10">
            Featured
          </Badge>
        )}
        {store.promotion && (
          <Badge className="absolute bottom-2 right-2 bg-warning text-warning-foreground z-10">
            <Tag className="w-3 h-3 mr-1" />
            {store.promotion.discount}
          </Badge>
        )}
      </div>

      <div className="p-4 space-y-3">
        {/* Store Header */}
        <div>
          <h3 className="font-semibold text-lg leading-tight">{store.name}</h3>
          <div className="flex items-center justify-between mt-1">
            <div className="flex items-center space-x-2 text-sm">
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span className="font-medium">{store.rating}</span>
              </div>
              <span className="text-muted-foreground">•</span>
              <div className="flex items-center space-x-1 text-muted-foreground">
                <MapPin className="w-3 h-3" />
                <span>{store.distance}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Address & Contact */}
        {!compact && (
          <div className="space-y-1 text-sm text-muted-foreground">
            <p className="flex items-start space-x-1">
              <MapPin className="w-3 h-3 mt-0.5 flex-shrink-0" />
              <span>{store.address}</span>
            </p>
            <p className="flex items-center space-x-1">
              <Phone className="w-3 h-3" />
              <span>{store.phone}</span>
            </p>
            <p className="flex items-center space-x-1">
              <Clock className="w-3 h-3" />
              <span>{store.hours}</span>
            </p>
          </div>
        )}

        {/* Specialties */}
        <div className="flex flex-wrap gap-1">
          {store.specialties
            .slice(0, compact ? 2 : 3)
            .map((specialty, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {specialty}
              </Badge>
            ))}
          {store.specialties.length > (compact ? 2 : 3) && (
            <Badge variant="secondary" className="text-xs">
              +{store.specialties.length - (compact ? 2 : 3)}
            </Badge>
          )}
        </div>

        {/* Promotion */}
        {store.promotion && (
          <div className="bg-promo-bg p-3 rounded-lg">
            <p className="text-sm font-medium text-primary">
              {store.promotion.title}
            </p>
            <p className="text-xs text-muted-foreground">
              Valid until {store.promotion.validUntil}
            </p>
          </div>
        )}

        {/* Actions */}
        <div className="flex space-x-2 pt-2">
          <Button
            variant="store"
            size="sm"
            className="flex-1"
            onClick={handleViewStore}
          >
            View Store
          </Button>
          {store.promotion && (
            <Button
              variant="promo"
              size="sm"
              className="flex-1"
              onClick={handleUseDiscount}
            >
              Use Discount
            </Button>
          )}
        </div>
      </div>
    </Card>
  );
};
