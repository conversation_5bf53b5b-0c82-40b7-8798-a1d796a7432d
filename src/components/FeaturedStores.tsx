import { StoreCard } from "./StoreCard";
import repairShopImage from "@/assets/repair-shop.jpg";

const featuredStores = [
  {
    id: "1",
    name: "TechFix Center",
    image: repairShopImage,
    rating: 4.8,
    distance: "0.5 km",
    address: "123 Avenue Habib Bourguiba, Tunis",
    phone: "+216 71 123 456",
    hours: "9:00 AM - 7:00 PM",
    specialties: ["iPhone Repair", "Samsung Repair", "Screen Replacement"],
    promotion: {
      title: "20% OFF Screen Repairs",
      discount: "20% OFF",
      validUntil: "Dec 31, 2024"
    },
    featured: true
  },
  {
    id: "2",
    name: "<PERSON> Doctor",
    image: repairShopImage,
    rating: 4.6,
    distance: "1.2 km",
    address: "456 Rue de la Liberté, Tunis",
    phone: "+216 71 234 567",
    hours: "8:30 AM - 8:00 PM",
    specialties: ["Water Damage", "Battery Replacement", "Data Recovery"],
    promotion: {
      title: "Free Diagnosis",
      discount: "FREE",
      validUntil: "Jan 15, 2025"
    },
    featured: true
  },
  {
    id: "3",
    name: "SmartPhone Plus",
    image: repairShopImage,
    rating: 4.7,
    distance: "2.0 km",
    address: "789 Avenue Mohamed V, Tunis",
    phone: "+216 71 345 678",
    hours: "10:00 AM - 6:00 PM",
    specialties: ["Xiaomi Repair", "OnePlus Repair", "Software Issues"],
    featured: true
  }
];

export const FeaturedStores = () => {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Featured Stores</h2>
        <button className="text-primary text-sm font-medium">View All</button>
      </div>
      
      <div className="flex space-x-4 overflow-x-auto pb-4 scrollbar-hide">
        {featuredStores.map((store) => (
          <StoreCard key={store.id} store={store} compact />
        ))}
      </div>
    </div>
  );
};