import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Search, MapPin, History, Phone } from "lucide-react";

const quickActions = [
  {
    icon: <Search className="w-6 h-6" />,
    title: "Track Repair",
    subtitle: "Check status",
    variant: "track" as const,
    action: "track"
  },
  {
    icon: <MapPin className="w-6 h-6" />,
    title: "Find Stores",
    subtitle: "Near you",
    variant: "store" as const,
    action: "stores"
  },
  {
    icon: <History className="w-6 h-6" />,
    title: "My Repairs",
    subtitle: "View history",
    variant: "secondary" as const,
    action: "history"
  },
  {
    icon: <Phone className="w-6 h-6" />,
    title: "Support",
    subtitle: "Get help",
    variant: "outline" as const,
    action: "support"
  }
];

export const QuickActions = () => {
  const navigate = useNavigate();

  const handleAction = (actionType: string) => {
    switch (actionType) {
      case 'track':
        // Scroll to repair tracker on homepage
        document.getElementById('repair-tracker')?.scrollIntoView({ behavior: 'smooth' });
        break;
      case 'stores':
        navigate('/stores');
        break;
      case 'history':
        navigate('/history');
        break;
      case 'support':
        navigate('/support');
        break;
    }
  };

  return (
    <div className="grid grid-cols-2 gap-3">
      {quickActions.map((action, index) => (
        <Card 
          key={index} 
          className="p-4 shadow-card hover:shadow-lg transition-all duration-200 cursor-pointer group"
          onClick={() => handleAction(action.action)}
        >
          <div className="text-center space-y-3">
            <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center text-primary group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
              {action.icon}
            </div>
            <div>
              <h3 className="font-semibold text-sm">{action.title}</h3>
              <p className="text-xs text-muted-foreground">{action.subtitle}</p>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};