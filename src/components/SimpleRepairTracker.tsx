import { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Search, Phone, Clock, CheckCircle, AlertCircle } from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface SimpleRepair {
  id: string;
  store_slug: string;
  store_name: string;
  customer_phone: string;
  device_model: string;
  issue_description: string;
  status:
    | "received"
    | "diagnosis"
    | "in_progress"
    | "completed"
    | "ready_for_pickup";
  estimated_cost?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export default function SimpleRepairTracker() {
  const [repairCode, setRepairCode] = useState("");
  const [loading, setLoading] = useState(false);
  const [repair, setRepair] = useState<SimpleRepair | null>(null);
  const [showResult, setShow<PERSON><PERSON>ult] = useState(false);

  const handleTrackRepair = async () => {
    if (!repairCode.trim()) {
      toast({
        title: "Repair code required",
        description: "Please enter your repair code to track your repair",
        variant: "destructive",
      });
      return;
    }

    // Validate repair code format (3 letter prefix + numbers)
    const codePattern = /^[A-Z]{3}\d+$/;
    if (!codePattern.test(repairCode.toUpperCase())) {
      toast({
        title: "Invalid repair code",
        description: "Repair code should be in format: ABC123456",
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);

      // Call the backend API with repair code
      const response = await fetch("/api/track-repair", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ repairCode: repairCode.toUpperCase() }),
      });

      if (!response.ok) {
        throw new Error("Repair not found");
      }

      const data = await response.json();
      const mockRepair: SimpleRepair = {
        id: data.repair.id || "1",
        store_slug: data.store || "techfix",
        store_name: data.store_name || "TechFix Center",
        customer_phone: data.repair.customer_phone || "+216 92 123 456",
        device_model: data.repair.device_model || "iPhone 14 Pro",
        issue_description: data.repair.issue_description || "Cracked screen",
        status: data.repair.status || "in_progress",
        estimated_cost: data.repair.estimated_cost || 180,
        notes: data.repair.notes || "Repair in progress. Will update soon.",
        created_at: data.repair.created_at || "2024-12-12T10:00:00Z",
        updated_at: data.repair.updated_at || "2024-12-13T14:30:00Z",
      };

      setRepair(mockRepair);
      setShowResult(true);

      toast({
        title: "Repair found!",
        description: `${mockRepair.device_model} repair at ${mockRepair.store_name}`,
      });
    } catch (error) {
      // Fallback to mock data for development/demo
      if (repairCode.toUpperCase().startsWith("TFX")) {
        const mockRepair: SimpleRepair = {
          id: "1",
          store_slug: "techfix",
          store_name: "TechFix Center",
          customer_phone: "+216 92 123 456",
          device_model: "iPhone 14 Pro",
          issue_description: "Cracked screen",
          status: "in_progress",
          estimated_cost: 180,
          notes:
            "Waiting for genuine Apple screen to arrive. Expected completion tomorrow.",
          created_at: "2024-12-12T10:00:00Z",
          updated_at: "2024-12-13T14:30:00Z",
        };
        setRepair(mockRepair);
        setShowResult(true);
        toast({
          title: "Repair found!",
          description: `${mockRepair.device_model} repair at ${mockRepair.store_name}`,
        });
      } else {
        toast({
          title: "No repair found",
          description:
            "No active repair found for this repair code. Please check the code and try again.",
          variant: "destructive",
        });
        setShowResult(false);
        setRepair(null);
      }
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      received: {
        label: "Received",
        variant: "secondary" as const,
        icon: Clock,
      },
      diagnosis: {
        label: "Diagnosis",
        variant: "default" as const,
        icon: Search,
      },
      in_progress: {
        label: "In Progress",
        variant: "default" as const,
        icon: AlertCircle,
      },
      completed: {
        label: "Completed",
        variant: "default" as const,
        icon: CheckCircle,
      },
      ready_for_pickup: {
        label: "Ready for Pickup",
        variant: "destructive" as const,
        icon: CheckCircle,
      },
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    if (!config) return null;

    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  const getStatusDescription = (status: string) => {
    const descriptions = {
      received: "Your device has been received and is in queue for diagnosis.",
      diagnosis:
        "Our technician is examining your device to determine the issue.",
      in_progress: "Repair work is currently in progress.",
      completed: "Repair has been completed and device is being tested.",
      ready_for_pickup:
        "Your device is ready for pickup! Please bring your ID.",
    };

    return (
      descriptions[status as keyof typeof descriptions] || "Status unknown"
    );
  };

  return (
    <div className="w-full max-w-2xl mx-auto space-y-6">
      {/* Search Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            Track Your Repair
          </CardTitle>
          <CardDescription>
            Enter your repair code to check the status of your device repair
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              type="text"
              placeholder="TFX123456"
              value={repairCode}
              onChange={(e) => setRepairCode(e.target.value.toUpperCase())}
              onKeyPress={(e) => e.key === "Enter" && handleTrackRepair()}
              className="flex-1 font-mono"
              maxLength={10}
            />
            <Button
              onClick={handleTrackRepair}
              disabled={loading}
              className="px-6"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                "Track"
              )}
            </Button>
          </div>
          <p className="text-sm text-gray-600">
            Use the repair code provided when you dropped off your device (e.g.,
            TFX123456)
          </p>
        </CardContent>
      </Card>

      {/* Results Section */}
      {showResult && repair && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Repair Status</CardTitle>
              {getStatusBadge(repair.status)}
            </div>
            <CardDescription>
              {repair.store_name} • Submitted{" "}
              {new Date(repair.created_at).toLocaleDateString()}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Device Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-900">Device</h4>
                <p className="text-gray-600">{repair.device_model}</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Issue</h4>
                <p className="text-gray-600">{repair.issue_description}</p>
              </div>
            </div>

            {/* Status Description */}
            <div className="p-3 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-900 mb-1">Current Status</h4>
              <p className="text-sm text-blue-700">
                {getStatusDescription(repair.status)}
              </p>
            </div>

            {/* Cost Estimate */}
            {repair.estimated_cost && (
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="font-medium">Estimated Cost</span>
                <span className="text-lg font-bold text-primary">
                  {repair.estimated_cost} TND
                </span>
              </div>
            )}

            {/* Notes */}
            {repair.notes && (
              <div>
                <h4 className="font-medium text-gray-900 mb-2">
                  Technician Notes
                </h4>
                <p className="text-gray-600 text-sm bg-gray-50 p-3 rounded-lg">
                  {repair.notes}
                </p>
              </div>
            )}

            {/* Contact Store */}
            <div className="flex gap-2 pt-4 border-t">
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => {
                  toast({
                    title: "Calling store",
                    description: `Calling ${repair.store_name}`,
                  });
                }}
              >
                <Phone className="w-4 h-4 mr-2" />
                Call Store
              </Button>
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => {
                  // Navigate to store details
                  window.location.href = `/store/${repair.store_slug}`;
                }}
              >
                Store Details
              </Button>
            </div>

            {/* Last Updated */}
            <p className="text-xs text-gray-500 text-center">
              Last updated: {new Date(repair.updated_at).toLocaleString()}
            </p>
          </CardContent>
        </Card>
      )}

      {/* No Login Required Notice */}
      <Card className="bg-green-50 border-green-200">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-green-800">
            <CheckCircle className="w-4 h-4" />
            <span className="text-sm font-medium">No account required</span>
          </div>
          <p className="text-sm text-green-700 mt-1">
            Simply use your repair code to track your repair status anytime.
            Your repair code is provided when you drop off your device.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
