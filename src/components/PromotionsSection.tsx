import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tag, Clock, Zap, Shield, Smartphone } from "lucide-react";

const promotions = [
  {
    id: "1",
    title: "Flash Sale: Screen Repairs",
    subtitle: "Limited time offer",
    discount: "30% OFF",
    description: "Professional screen replacement for all phone models",
    validUntil: "2 days left",
    icon: <Zap className="w-6 h-6" />,
    bgColor: "bg-gradient-to-r from-yellow-100 to-orange-100",
    textColor: "text-orange-600"
  },
  {
    id: "2",
    title: "Free Diagnostic Check",
    subtitle: "Know what's wrong",
    discount: "FREE",
    description: "Complete phone diagnosis with no hidden charges",
    validUntil: "This month",
    icon: <Shield className="w-6 h-6" />,
    bgColor: "bg-gradient-to-r from-green-100 to-emerald-100",
    textColor: "text-green-600"
  },
  {
    id: "3",
    title: "Student Discount",
    subtitle: "Valid student ID required",
    discount: "15% OFF",
    description: "Special pricing for students on all repairs",
    validUntil: "Ongoing",
    icon: <Smartphone className="w-6 h-6" />,
    bgColor: "bg-gradient-to-r from-blue-100 to-indigo-100",
    textColor: "text-blue-600"
  }
];

export const PromotionsSection = () => {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold">Current Promotions</h2>
        <button className="text-primary text-sm font-medium">View All</button>
      </div>
      
      <div className="flex space-x-4 overflow-x-auto pb-4 scrollbar-hide">
        {promotions.map((promo) => (
          <Card key={promo.id} className="w-72 flex-shrink-0 shadow-card hover:shadow-lg transition-all duration-200">
            <div className={`${promo.bgColor} p-4 rounded-t-lg`}>
              <div className="flex items-start justify-between">
                <div className={`${promo.textColor} space-y-1`}>
                  <div className="flex items-center space-x-2">
                    {promo.icon}
                    <Badge className="bg-white/20 text-current border-0">
                      {promo.discount}
                    </Badge>
                  </div>
                  <h3 className="font-bold text-lg leading-tight">{promo.title}</h3>
                  <p className="text-sm opacity-80">{promo.subtitle}</p>
                </div>
              </div>
            </div>
            
            <div className="p-4 space-y-4">
              <p className="text-sm text-muted-foreground">{promo.description}</p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                  <Clock className="w-3 h-3" />
                  <span>{promo.validUntil}</span>
                </div>
              </div>
              
              <Button variant="promo" size="sm" className="w-full">
                <Tag className="w-4 h-4" />
                Claim Offer
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};