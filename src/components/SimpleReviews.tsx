import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Star, MessageSquare, Plus } from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface Review {
  id: string;
  rating: number;
  comment: string;
  repair_type: string;
  created_at: string;
}

interface SimpleReviewsProps {
  storeSlug: string;
  storeName: string;
  reviews: Review[];
  averageRating: number;
  totalReviews: number;
  onNewReview?: (review: Omit<Review, 'id' | 'created_at'>) => void;
}

export default function SimpleReviews({ 
  storeSlug, 
  storeName, 
  reviews, 
  averageRating, 
  totalReviews,
  onNewReview 
}: SimpleReviewsProps) {
  const [showAddReview, setShowAddReview] = useState(false);
  const [newReview, setNewReview] = useState({
    rating: 0,
    comment: "",
    repair_type: ""
  });
  const [submitting, setSubmitting] = useState(false);

  const handleStarClick = (rating: number) => {
    setNewReview(prev => ({ ...prev, rating }));
  };

  const handleSubmitReview = async () => {
    if (newReview.rating === 0) {
      toast({
        title: "Rating required",
        description: "Please select a star rating",
        variant: "destructive"
      });
      return;
    }

    if (!newReview.comment.trim()) {
      toast({
        title: "Comment required",
        description: "Please write a comment about your experience",
        variant: "destructive"
      });
      return;
    }

    try {
      setSubmitting(true);
      
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate successful submission
      const review = {
        rating: newReview.rating,
        comment: newReview.comment.trim(),
        repair_type: newReview.repair_type.trim() || "General"
      };

      if (onNewReview) {
        onNewReview(review);
      }

      toast({
        title: "Review submitted!",
        description: "Thank you for your feedback",
      });

      // Reset form
      setNewReview({ rating: 0, comment: "", repair_type: "" });
      setShowAddReview(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit review. Please try again.",
        variant: "destructive"
      });
    } finally {
      setSubmitting(false);
    }
  };

  const renderStars = (rating: number, interactive = false, size = "w-4 h-4") => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${size} ${
              star <= rating 
                ? "fill-yellow-400 text-yellow-400" 
                : "text-gray-300"
            } ${interactive ? "cursor-pointer hover:text-yellow-400" : ""}`}
            onClick={interactive ? () => handleStarClick(star) : undefined}
          />
        ))}
      </div>
    );
  };

  const getRepairTypeBadge = (repairType: string) => {
    const colors = [
      "bg-blue-100 text-blue-800",
      "bg-green-100 text-green-800", 
      "bg-purple-100 text-purple-800",
      "bg-orange-100 text-orange-800",
      "bg-pink-100 text-pink-800"
    ];
    
    const colorIndex = repairType.length % colors.length;
    
    return (
      <Badge variant="secondary" className={colors[colorIndex]}>
        {repairType}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Reviews Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="w-5 h-5" />
                Customer Reviews
              </CardTitle>
              <CardDescription>
                {totalReviews} review{totalReviews !== 1 ? 's' : ''} for {storeName}
              </CardDescription>
            </div>
            <div className="text-right">
              <div className="flex items-center gap-2">
                {renderStars(Math.round(averageRating))}
                <span className="text-2xl font-bold">{averageRating.toFixed(1)}</span>
              </div>
              <p className="text-sm text-gray-600">{totalReviews} reviews</p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={() => setShowAddReview(!showAddReview)}
            className="w-full"
            variant={showAddReview ? "outline" : "default"}
          >
            <Plus className="w-4 h-4 mr-2" />
            {showAddReview ? "Cancel" : "Write a Review"}
          </Button>
        </CardContent>
      </Card>

      {/* Add Review Form */}
      {showAddReview && (
        <Card>
          <CardHeader>
            <CardTitle>Write a Review</CardTitle>
            <CardDescription>
              Share your experience to help other customers
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label>Rating *</Label>
              <div className="flex items-center gap-2 mt-1">
                {renderStars(newReview.rating, true, "w-6 h-6")}
                <span className="text-sm text-gray-600 ml-2">
                  {newReview.rating > 0 ? `${newReview.rating} star${newReview.rating !== 1 ? 's' : ''}` : "Select rating"}
                </span>
              </div>
            </div>

            <div>
              <Label htmlFor="repair_type">Repair Type (Optional)</Label>
              <Input
                id="repair_type"
                value={newReview.repair_type}
                onChange={(e) => setNewReview(prev => ({ ...prev, repair_type: e.target.value }))}
                placeholder="e.g., Screen Repair, Battery Replacement"
              />
            </div>

            <div>
              <Label htmlFor="comment">Your Review *</Label>
              <Textarea
                id="comment"
                value={newReview.comment}
                onChange={(e) => setNewReview(prev => ({ ...prev, comment: e.target.value }))}
                placeholder="Tell others about your experience..."
                rows={4}
              />
            </div>

            <div className="flex gap-2">
              <Button 
                onClick={handleSubmitReview}
                disabled={submitting}
                className="flex-1"
              >
                {submitting ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : null}
                Submit Review
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setShowAddReview(false)}
                disabled={submitting}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reviews List */}
      <div className="space-y-4">
        {reviews.length === 0 ? (
          <Card>
            <CardContent className="pt-6 text-center">
              <MessageSquare className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="font-medium text-gray-900 mb-2">No reviews yet</h3>
              <p className="text-gray-600 text-sm">
                Be the first to share your experience with {storeName}
              </p>
            </CardContent>
          </Card>
        ) : (
          reviews.map((review) => (
            <Card key={review.id}>
              <CardContent className="pt-6">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-3">
                    {renderStars(review.rating)}
                    <span className="text-sm text-gray-600">
                      {new Date(review.created_at).toLocaleDateString()}
                    </span>
                  </div>
                  {review.repair_type && getRepairTypeBadge(review.repair_type)}
                </div>
                <p className="text-gray-700 leading-relaxed">{review.comment}</p>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Anonymous Notice */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-blue-800">
            <MessageSquare className="w-4 h-4" />
            <span className="text-sm font-medium">Anonymous Reviews</span>
          </div>
          <p className="text-sm text-blue-700 mt-1">
            Reviews are posted anonymously. No personal information is required or stored.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
