import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Plus, Search, Phone, Edit, Eye } from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface SimpleRepair {
  id: string;
  store_slug: string;
  store_name: string;
  customer_phone: string;
  device_model: string;
  issue_description: string;
  status: 'received' | 'diagnosis' | 'in_progress' | 'completed' | 'ready_for_pickup';
  estimated_cost?: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export default function AdminRepairs() {
  const navigate = useNavigate();
  const [repairs, setRepairs] = useState<SimpleRepair[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  
  const [newRepair, setNewRepair] = useState({
    store_slug: "",
    customer_phone: "",
    device_model: "",
    issue_description: "",
    estimated_cost: "",
    notes: ""
  });

  useEffect(() => {
    fetchRepairs();
  }, []);

  const fetchRepairs = async () => {
    try {
      setLoading(true);
      // TODO: Replace with actual API call
      // const response = await fetch('/api/admin/repairs');
      // const data = await response.json();
      
      // Mock data for now
      const mockRepairs: SimpleRepair[] = [
        {
          id: "1",
          store_slug: "mobile-doctor",
          store_name: "Mobile Doctor",
          customer_phone: "+216 92 123 456",
          device_model: "iPhone 14 Pro",
          issue_description: "Cracked screen",
          status: "in_progress",
          estimated_cost: 180,
          notes: "Waiting for genuine Apple screen to arrive",
          created_at: "2024-12-12T10:00:00Z",
          updated_at: "2024-12-13T14:30:00Z"
        },
        {
          id: "2",
          store_slug: "fix-it-fast",
          store_name: "Fix It Fast",
          customer_phone: "+216 97 888 999",
          device_model: "Samsung Galaxy S23",
          issue_description: "Battery draining fast",
          status: "diagnosis",
          estimated_cost: 120,
          notes: "Running battery diagnostics",
          created_at: "2024-12-13T09:00:00Z",
          updated_at: "2024-12-13T15:00:00Z"
        }
      ];
      
      setRepairs(mockRepairs);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch repairs",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddRepair = async () => {
    try {
      // TODO: Replace with actual API call
      const repairId = Date.now().toString();
      const repair: SimpleRepair = {
        id: repairId,
        ...newRepair,
        store_name: "Store Name", // This would come from the API
        status: "received",
        estimated_cost: newRepair.estimated_cost ? parseFloat(newRepair.estimated_cost) : undefined,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      setRepairs(prev => [repair, ...prev]);
      setNewRepair({
        store_slug: "",
        customer_phone: "",
        device_model: "",
        issue_description: "",
        estimated_cost: "",
        notes: ""
      });
      setShowAddForm(false);
      
      toast({
        title: "Success",
        description: `Repair created with code: ${newRepair.store_slug.toUpperCase()}${repairId}`
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create repair",
        variant: "destructive"
      });
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      received: { label: 'Received', variant: 'secondary' as const },
      diagnosis: { label: 'Diagnosis', variant: 'default' as const },
      in_progress: { label: 'In Progress', variant: 'default' as const },
      completed: { label: 'Completed', variant: 'default' as const },
      ready_for_pickup: { label: 'Ready', variant: 'destructive' as const }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return config ? <Badge variant={config.variant}>{config.label}</Badge> : null;
  };

  const filteredRepairs = repairs.filter(repair => {
    const matchesSearch = repair.device_model.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         repair.customer_phone.includes(searchQuery) ||
                         repair.issue_description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === "all" || repair.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-secondary/20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading repairs...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-secondary/20 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button variant="outline" onClick={() => navigate('/admin')}>
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-gray-900">Repair Management</h1>
            <p className="text-gray-600 mt-2">Manage repairs for directory stores</p>
          </div>
          <Button onClick={() => setShowAddForm(!showAddForm)}>
            <Plus className="w-4 h-4 mr-2" />
            {showAddForm ? "Cancel" : "Add Repair"}
          </Button>
        </div>

        {/* Add Repair Form */}
        {showAddForm && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Add New Repair</CardTitle>
              <CardDescription>Create a new repair entry for a directory store</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="store_slug">Store Slug</Label>
                  <Input
                    id="store_slug"
                    value={newRepair.store_slug}
                    onChange={(e) => setNewRepair(prev => ({ ...prev, store_slug: e.target.value }))}
                    placeholder="mobile-doctor"
                  />
                </div>
                <div>
                  <Label htmlFor="customer_phone">Customer Phone</Label>
                  <Input
                    id="customer_phone"
                    value={newRepair.customer_phone}
                    onChange={(e) => setNewRepair(prev => ({ ...prev, customer_phone: e.target.value }))}
                    placeholder="+216 92 123 456"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="device_model">Device Model</Label>
                  <Input
                    id="device_model"
                    value={newRepair.device_model}
                    onChange={(e) => setNewRepair(prev => ({ ...prev, device_model: e.target.value }))}
                    placeholder="iPhone 14 Pro"
                  />
                </div>
                <div>
                  <Label htmlFor="estimated_cost">Estimated Cost (TND)</Label>
                  <Input
                    id="estimated_cost"
                    type="number"
                    value={newRepair.estimated_cost}
                    onChange={(e) => setNewRepair(prev => ({ ...prev, estimated_cost: e.target.value }))}
                    placeholder="180"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="issue_description">Issue Description</Label>
                <Textarea
                  id="issue_description"
                  value={newRepair.issue_description}
                  onChange={(e) => setNewRepair(prev => ({ ...prev, issue_description: e.target.value }))}
                  placeholder="Describe the issue..."
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  value={newRepair.notes}
                  onChange={(e) => setNewRepair(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Additional notes..."
                  rows={2}
                />
              </div>

              <div className="flex gap-2">
                <Button onClick={handleAddRepair} className="flex-1">
                  Create Repair
                </Button>
                <Button variant="outline" onClick={() => setShowAddForm(false)}>
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search repairs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="received">Received</SelectItem>
                  <SelectItem value="diagnosis">Diagnosis</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="ready_for_pickup">Ready for Pickup</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Repairs List */}
        <div className="space-y-4">
          {filteredRepairs.map((repair) => (
            <Card key={repair.id}>
              <CardContent className="pt-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-semibold">{repair.device_model}</h3>
                      {getStatusBadge(repair.status)}
                      <Badge variant="outline">
                        {repair.store_slug.toUpperCase()}{repair.id}
                      </Badge>
                    </div>
                    <p className="text-gray-600 text-sm">{repair.issue_description}</p>
                    <p className="text-gray-500 text-sm mt-1">
                      {repair.store_name} • {new Date(repair.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    {repair.estimated_cost && (
                      <p className="font-semibold text-lg">{repair.estimated_cost} TND</p>
                    )}
                    <div className="flex gap-2 mt-2">
                      <Button variant="outline" size="sm">
                        <Phone className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
                
                {repair.notes && (
                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-700">{repair.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredRepairs.length === 0 && (
          <Card>
            <CardContent className="pt-6 text-center">
              <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="font-medium text-gray-900 mb-2">No repairs found</h3>
              <p className="text-gray-600 text-sm">
                {searchQuery || statusFilter !== "all" 
                  ? "Try adjusting your search or filters"
                  : "Create your first repair to get started"
                }
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
