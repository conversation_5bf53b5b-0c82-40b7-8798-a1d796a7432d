import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Save, Plus, X } from "lucide-react";
import { toast } from "@/hooks/use-toast";

const storeSchema = z.object({
  slug: z.string().min(1, "Slug is required").regex(/^[a-z0-9-]+$/, "Slug must contain only lowercase letters, numbers, and hyphens"),
  name: z.string().min(1, "Store name is required"),
  address: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email("Invalid email").optional().or(z.literal("")),
  storeType: z.enum(["subscription", "directory"]),
  visibilityTier: z.enum(["free", "premium", "sponsored"]),
  description: z.string().optional(),
  website: z.string().url("Invalid URL").optional().or(z.literal("")),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  featured: z.boolean().default(false),
  active: z.boolean().default(true),
  // Subscription store fields
  repairCodePrefix: z.string().optional(),
  supabaseUrl: z.string().optional(),
  supabaseKey: z.string().optional(),
});

type StoreFormData = z.infer<typeof storeSchema>;

export default function AdminStoreForm() {
  const navigate = useNavigate();
  const { slug } = useParams();
  const isEditing = !!slug;
  
  const [loading, setLoading] = useState(false);
  const [services, setServices] = useState<string[]>([]);
  const [specialties, setSpecialties] = useState<string[]>([]);
  const [newService, setNewService] = useState("");
  const [newSpecialty, setNewSpecialty] = useState("");
  const [hours, setHours] = useState<Record<string, string>>({
    monday: "9:00-18:00",
    tuesday: "9:00-18:00",
    wednesday: "9:00-18:00",
    thursday: "9:00-18:00",
    friday: "9:00-18:00",
    saturday: "10:00-16:00",
    sunday: "closed"
  });

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
    reset
  } = useForm<StoreFormData>({
    resolver: zodResolver(storeSchema),
    defaultValues: {
      storeType: "directory",
      visibilityTier: "free",
      featured: false,
      active: true
    }
  });

  const storeType = watch("storeType");

  useEffect(() => {
    if (isEditing) {
      fetchStore();
    }
  }, [isEditing, slug]);

  const fetchStore = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/stores/${slug}`);
      const store = await response.json();
      
      // Populate form with existing data
      reset({
        slug: store.slug,
        name: store.name,
        address: store.address || "",
        phone: store.phone || "",
        email: store.email || "",
        storeType: store.store_type,
        visibilityTier: store.visibility_tier,
        description: store.description || "",
        website: store.website || "",
        latitude: store.latitude,
        longitude: store.longitude,
        featured: store.featured,
        active: store.active
      });
      
      setServices(store.services || []);
      setSpecialties(store.specialties || []);
      setHours(store.hours || hours);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch store data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: StoreFormData) => {
    try {
      setLoading(true);
      
      const payload = {
        ...data,
        services,
        specialties,
        hours
      };

      const url = isEditing ? `/api/stores/${slug}` : '/api/stores';
      const method = isEditing ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: `Store ${isEditing ? 'updated' : 'created'} successfully`
        });
        navigate('/admin');
      } else {
        const error = await response.json();
        throw new Error(error.message || 'Failed to save store');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save store",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const addService = () => {
    if (newService.trim() && !services.includes(newService.trim())) {
      setServices([...services, newService.trim()]);
      setNewService("");
    }
  };

  const removeService = (service: string) => {
    setServices(services.filter(s => s !== service));
  };

  const addSpecialty = () => {
    if (newSpecialty.trim() && !specialties.includes(newSpecialty.trim())) {
      setSpecialties([...specialties, newSpecialty.trim()]);
      setNewSpecialty("");
    }
  };

  const removeSpecialty = (specialty: string) => {
    setSpecialties(specialties.filter(s => s !== specialty));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-secondary/20 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button variant="outline" onClick={() => navigate('/admin')}>
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {isEditing ? 'Edit Store' : 'Add New Store'}
            </h1>
            <p className="text-gray-600 mt-2">
              {isEditing ? 'Update store information and settings' : 'Create a new store listing'}
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
              <CardDescription>
                Essential store details and contact information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="slug">Store Slug *</Label>
                  <Input
                    id="slug"
                    {...register("slug")}
                    placeholder="my-repair-shop"
                    disabled={isEditing}
                  />
                  {errors.slug && (
                    <p className="text-sm text-red-600 mt-1">{errors.slug.message}</p>
                  )}
                </div>
                
                <div>
                  <Label htmlFor="name">Store Name *</Label>
                  <Input
                    id="name"
                    {...register("name")}
                    placeholder="My Repair Shop"
                  />
                  {errors.name && (
                    <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="address">Address</Label>
                <Input
                  id="address"
                  {...register("address")}
                  placeholder="123 Main Street, City"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    {...register("phone")}
                    placeholder="+216 71 123 456"
                  />
                </div>
                
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    {...register("email")}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="text-sm text-red-600 mt-1">{errors.email.message}</p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  {...register("description")}
                  placeholder="Brief description of your store and services..."
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  {...register("website")}
                  placeholder="https://mystore.com"
                />
                {errors.website && (
                  <p className="text-sm text-red-600 mt-1">{errors.website.message}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Store Type & Visibility */}
          <Card>
            <CardHeader>
              <CardTitle>Store Type & Visibility</CardTitle>
              <CardDescription>
                Configure store type and visibility settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="storeType">Store Type</Label>
                  <Select onValueChange={(value) => setValue("storeType", value as "subscription" | "directory")}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select store type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="directory">Directory Store</SelectItem>
                      <SelectItem value="subscription">Subscription Store</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="visibilityTier">Visibility Tier</Label>
                  <Select onValueChange={(value) => setValue("visibilityTier", value as "free" | "premium" | "sponsored")}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select visibility tier" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="free">Free</SelectItem>
                      <SelectItem value="premium">Premium</SelectItem>
                      <SelectItem value="sponsored">Sponsored</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="featured">Featured Store</Label>
                  <p className="text-sm text-gray-600">Display this store prominently</p>
                </div>
                <Switch
                  id="featured"
                  onCheckedChange={(checked) => setValue("featured", checked)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="active">Active</Label>
                  <p className="text-sm text-gray-600">Store is visible to users</p>
                </div>
                <Switch
                  id="active"
                  onCheckedChange={(checked) => setValue("active", checked)}
                />
              </div>
            </CardContent>
          </Card>

          {/* Subscription Store Fields */}
          {storeType === "subscription" && (
            <Card>
              <CardHeader>
                <CardTitle>Subscription Store Settings</CardTitle>
                <CardDescription>
                  Configuration for stores with their own repair tracking system
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="repairCodePrefix">Repair Code Prefix (3 chars)</Label>
                  <Input
                    id="repairCodePrefix"
                    {...register("repairCodePrefix")}
                    placeholder="ABC"
                    maxLength={3}
                  />
                </div>
                
                <div>
                  <Label htmlFor="supabaseUrl">Supabase URL</Label>
                  <Input
                    id="supabaseUrl"
                    {...register("supabaseUrl")}
                    placeholder="https://your-project.supabase.co"
                  />
                </div>
                
                <div>
                  <Label htmlFor="supabaseKey">Supabase Anon Key</Label>
                  <Input
                    id="supabaseKey"
                    type="password"
                    {...register("supabaseKey")}
                    placeholder="Your Supabase anon key"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Services & Specialties */}
          <Card>
            <CardHeader>
              <CardTitle>Services & Specialties</CardTitle>
              <CardDescription>
                Add services offered and store specialties
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Services Offered</Label>
                <div className="flex gap-2 mt-2">
                  <Input
                    value={newService}
                    onChange={(e) => setNewService(e.target.value)}
                    placeholder="Add a service..."
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addService())}
                  />
                  <Button type="button" onClick={addService} size="sm">
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {services.map((service) => (
                    <Badge key={service} variant="secondary" className="flex items-center gap-1">
                      {service}
                      <X className="w-3 h-3 cursor-pointer" onClick={() => removeService(service)} />
                    </Badge>
                  ))}
                </div>
              </div>

              <div>
                <Label>Specialties</Label>
                <div className="flex gap-2 mt-2">
                  <Input
                    value={newSpecialty}
                    onChange={(e) => setNewSpecialty(e.target.value)}
                    placeholder="Add a specialty..."
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSpecialty())}
                  />
                  <Button type="button" onClick={addSpecialty} size="sm">
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2 mt-2">
                  {specialties.map((specialty) => (
                    <Badge key={specialty} variant="outline" className="flex items-center gap-1">
                      {specialty}
                      <X className="w-3 h-3 cursor-pointer" onClick={() => removeSpecialty(specialty)} />
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={() => navigate('/admin')}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              <Save className="w-4 h-4 mr-2" />
              {loading ? 'Saving...' : (isEditing ? 'Update Store' : 'Create Store')}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
