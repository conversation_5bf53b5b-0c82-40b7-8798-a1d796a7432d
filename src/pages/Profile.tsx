import { useNavigate } from 'react-router-dom';
import { ArrowLeft, User, Phone, Mail, Calendar, Settings, LogOut, History, Heart, Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { mockUserProfile } from '@/data/mockData';
import { toast } from '@/hooks/use-toast';

export default function Profile() {
  const navigate = useNavigate();

  const handleAction = (action: string) => {
    toast({
      title: action,
      description: `${action} feature coming soon...`,
    });
  };

  const menuItems = [
    {
      icon: <History className="w-5 h-5" />,
      title: "Repair History",
      description: "View all your past repairs",
      action: () => navigate('/history')
    },
    {
      icon: <Heart className="w-5 h-5" />,
      title: "Favorite Stores",
      description: "Your saved repair stores",
      action: () => handleAction("Favorite Stores")
    },
    {
      icon: <Bell className="w-5 h-5" />,
      title: "Notifications",
      description: "Manage your notification preferences",
      action: () => handleAction("Notifications")
    },
    {
      icon: <Settings className="w-5 h-5" />,
      title: "Settings",
      description: "App preferences and account settings",
      action: () => handleAction("Settings")
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-secondary/20">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white/95 backdrop-blur-sm border-b">
        <div className="container mx-auto px-4 py-3 max-w-md">
          <div className="flex items-center justify-between">
            <Button 
              variant="ghost" 
              size="icon"
              onClick={() => navigate('/')}
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <h1 className="font-semibold">Profile</h1>
            <Button 
              variant="ghost" 
              size="icon"
              onClick={() => handleAction("Edit Profile")}
            >
              <Settings className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 space-y-6 max-w-md">
        {/* Profile Header */}
        <Card className="p-6">
          <div className="flex items-center space-x-4">
            <Avatar className="w-16 h-16">
              <AvatarFallback className="bg-primary text-primary-foreground text-xl font-bold">
                {mockUserProfile.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <h2 className="text-xl font-bold">{mockUserProfile.name}</h2>
              <p className="text-muted-foreground">Member since {mockUserProfile.memberSince}</p>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4 mt-6 pt-4 border-t">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{mockUserProfile.totalRepairs}</div>
              <div className="text-xs text-muted-foreground">Total Repairs</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">4.9</div>
              <div className="text-xs text-muted-foreground">Average Rating</div>
            </div>
          </div>
        </Card>

        {/* Contact Information */}
        <Card className="p-4 space-y-3">
          <h3 className="font-semibold">Contact Information</h3>
          
          <div className="flex items-center space-x-3">
            <Phone className="w-5 h-5 text-muted-foreground" />
            <span className="text-sm">{mockUserProfile.phone}</span>
          </div>
          
          <div className="flex items-center space-x-3">
            <Mail className="w-5 h-5 text-muted-foreground" />
            <span className="text-sm">{mockUserProfile.email}</span>
          </div>
          
          <div className="flex items-center space-x-3">
            <Calendar className="w-5 h-5 text-muted-foreground" />
            <span className="text-sm">Member since {mockUserProfile.memberSince}</span>
          </div>
        </Card>

        {/* Menu Items */}
        <div className="space-y-2">
          {menuItems.map((item, index) => (
            <Card key={index} className="p-4 cursor-pointer hover:shadow-lg transition-shadow">
              <div 
                className="flex items-center space-x-4"
                onClick={item.action}
              >
                <div className="p-2 bg-primary/10 rounded-lg text-primary">
                  {item.icon}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium">{item.title}</h4>
                  <p className="text-sm text-muted-foreground">{item.description}</p>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Logout */}
        <Card className="p-4">
          <Button 
            variant="destructive" 
            className="w-full"
            onClick={() => handleAction("Logout")}
          >
            <LogOut className="w-4 h-4 mr-2" />
            Sign Out
          </Button>
        </Card>

        {/* App Info */}
        <div className="text-center text-xs text-muted-foreground space-y-1">
          <p>Talifouni+ v1.0.0</p>
          <p>Made with ❤️ in Tunisia</p>
        </div>
      </div>
    </div>
  );
}