import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Calendar, MapPin, DollarSign } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { mockRepairs } from '@/data/mockData';

export default function RepairHistory() {
  const navigate = useNavigate();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'in-progress':
        return 'bg-blue-100 text-blue-700 border-blue-200';
      case 'diagnosis':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'in-progress':
        return 'In Progress';
      case 'diagnosis':
        return 'Under Diagnosis';
      case 'pending':
        return 'Pending';
      case 'ready':
        return 'Ready for Pickup';
      default:
        return status;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-secondary/20">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white/95 backdrop-blur-sm border-b">
        <div className="container mx-auto px-4 py-3 max-w-md">
          <div className="flex items-center justify-between">
            <Button 
              variant="ghost" 
              size="icon"
              onClick={() => navigate('/')}
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <h1 className="font-semibold">Repair History</h1>
            <div className="w-10" />
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 space-y-6 max-w-md">
        {/* Stats */}
        <Card className="p-4">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-primary">{mockRepairs.length}</div>
              <div className="text-xs text-muted-foreground">Total Repairs</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                {mockRepairs.filter(r => r.status === 'completed').length}
              </div>
              <div className="text-xs text-muted-foreground">Completed</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {mockRepairs.filter(r => r.status === 'in-progress').length}
              </div>
              <div className="text-xs text-muted-foreground">Active</div>
            </div>
          </div>
        </Card>

        {/* Repair List */}
        <div className="space-y-4">
          {mockRepairs.map((repair) => (
            <Card key={repair.id} className="p-4 cursor-pointer hover:shadow-lg transition-shadow">
              <div className="space-y-3">
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="font-semibold">{repair.deviceModel}</h3>
                    <p className="text-sm text-muted-foreground">{repair.issueType}</p>
                  </div>
                  <Badge className={getStatusColor(repair.status)}>
                    {getStatusText(repair.status)}
                  </Badge>
                </div>

                {/* Store & Date */}
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <MapPin className="w-3 h-3" />
                    <span>{repair.storeName}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-3 h-3" />
                    <span>{repair.dateSubmitted}</span>
                  </div>
                </div>

                {/* Cost */}
                {repair.cost && (
                  <div className="flex items-center space-x-1 text-sm">
                    <DollarSign className="w-3 h-3 text-muted-foreground" />
                    <span className="font-medium">{repair.cost} TND</span>
                  </div>
                )}

                {/* Progress */}
                <div className="text-xs text-muted-foreground">
                  Repair Code: {repair.repairCode}
                </div>

                {/* Action */}
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full"
                  onClick={() => navigate(`/?track=${repair.repairCode}`)}
                >
                  View Details
                </Button>
              </div>
            </Card>
          ))}
        </div>

        {mockRepairs.length === 0 && (
          <div className="text-center py-8">
            <p className="text-muted-foreground mb-4">No repair history found.</p>
            <Button onClick={() => navigate('/')}>
              Start a Repair
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}