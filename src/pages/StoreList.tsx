import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Search, Filter, MapPin } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { StoreCard } from "@/components/StoreCard";
import { mockStores } from "@/data/mockData";

export default function StoreList() {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<"distance" | "rating" | "name">(
    "distance"
  );

  const filteredStores = mockStores
    .filter(
      (store) =>
        store.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        store.specialties.some((specialty) =>
          specialty.toLowerCase().includes(searchQuery.toLowerCase())
        )
    )
    .sort((a, b) => {
      // First sort by visibility tier (sponsored > premium > free)
      const tierOrder = { sponsored: 3, premium: 2, free: 1 };
      const aTier = tierOrder[a.visibilityTier || "free"];
      const bTier = tierOrder[b.visibilityTier || "free"];

      if (aTier !== bTier) {
        return bTier - aTier;
      }

      // Then by featured status
      if (a.featured !== b.featured) {
        return (b.featured ? 1 : 0) - (a.featured ? 1 : 0);
      }

      // Finally by the selected sort criteria
      switch (sortBy) {
        case "rating":
          return b.rating - a.rating;
        case "name":
          return a.name.localeCompare(b.name);
        case "distance":
        default:
          return parseFloat(a.distance) - parseFloat(b.distance);
      }
    });

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-secondary/20">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white/95 backdrop-blur-sm border-b">
        <div className="container mx-auto px-4 py-3 max-w-md">
          <div className="flex items-center justify-between">
            <Button variant="ghost" size="icon" onClick={() => navigate("/")}>
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <h1 className="font-semibold">Repair Stores</h1>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                /* TODO: Implement map view */
              }}
            >
              <MapPin className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 space-y-6 max-w-md">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="Search stores or services..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Filters */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Sort by:</span>
          </div>
          <div className="flex space-x-2">
            {[
              { key: "distance", label: "Distance" },
              { key: "rating", label: "Rating" },
              { key: "name", label: "Name" },
            ].map(({ key, label }) => (
              <Button
                key={key}
                variant={sortBy === key ? "default" : "outline"}
                size="sm"
                onClick={() => setSortBy(key as any)}
              >
                {label}
              </Button>
            ))}
          </div>
        </div>

        {/* Results Count */}
        <div className="text-sm text-muted-foreground">
          {filteredStores.length} store{filteredStores.length !== 1 ? "s" : ""}{" "}
          found
        </div>

        {/* Store List */}
        <div className="space-y-4">
          {filteredStores.map((store) => (
            <div key={store.id} onClick={() => navigate(`/store/${store.id}`)}>
              <StoreCard store={store} />
            </div>
          ))}
        </div>

        {filteredStores.length === 0 && (
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              No stores found matching your search.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
