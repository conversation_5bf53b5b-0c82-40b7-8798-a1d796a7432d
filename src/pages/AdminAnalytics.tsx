import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  ArrowLeft, 
  TrendingUp, 
  Eye, 
  Phone, 
  MousePointer,
  Calendar,
  BarChart3,
  Users
} from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface AnalyticsData {
  store_slug: string;
  event_type: string;
  event_date: string;
  event_count: number;
  metadata?: any;
}

interface StoreInfo {
  slug: string;
  name: string;
  visibility_tier: string;
  total_clicks: number;
  monthly_clicks: number;
  featured: boolean;
}

export default function AdminAnalytics() {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState("30");
  const [analytics, setAnalytics] = useState<AnalyticsData[]>([]);
  const [storeInfo, setStoreInfo] = useState<StoreInfo | null>(null);
  const [summary, setSummary] = useState({
    totalViews: 0,
    totalClicks: 0,
    totalCalls: 0,
    conversionRate: 0,
    avgDailyViews: 0,
    topDays: [] as { date: string; views: number }[]
  });

  useEffect(() => {
    if (slug) {
      fetchAnalytics();
      fetchStoreInfo();
    }
  }, [slug, dateRange]);

  const fetchStoreInfo = async () => {
    try {
      const response = await fetch(`/api/stores/${slug}`);
      const data = await response.json();
      setStoreInfo(data);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch store information",
        variant: "destructive"
      });
    }
  };

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - parseInt(dateRange));
      
      const response = await fetch(
        `/api/stores/${slug}/analytics?startDate=${startDate.toISOString().split('T')[0]}&endDate=${endDate.toISOString().split('T')[0]}`
      );
      const data = await response.json();
      setAnalytics(data.analytics);
      
      // Calculate summary statistics
      const views = data.analytics.filter((a: AnalyticsData) => a.event_type === 'view');
      const clicks = data.analytics.filter((a: AnalyticsData) => a.event_type === 'click');
      const calls = data.analytics.filter((a: AnalyticsData) => a.event_type === 'call');
      
      const totalViews = views.reduce((sum: number, a: AnalyticsData) => sum + a.event_count, 0);
      const totalClicks = clicks.reduce((sum: number, a: AnalyticsData) => sum + a.event_count, 0);
      const totalCalls = calls.reduce((sum: number, a: AnalyticsData) => sum + a.event_count, 0);
      
      const conversionRate = totalViews > 0 ? ((totalClicks + totalCalls) / totalViews) * 100 : 0;
      const avgDailyViews = totalViews / parseInt(dateRange);
      
      // Get top performing days
      const viewsByDate = views.reduce((acc: Record<string, number>, a: AnalyticsData) => {
        acc[a.event_date] = (acc[a.event_date] || 0) + a.event_count;
        return acc;
      }, {});
      
      const topDays = Object.entries(viewsByDate)
        .map(([date, views]) => ({ date, views: views as number }))
        .sort((a, b) => b.views - a.views)
        .slice(0, 5);
      
      setSummary({
        totalViews,
        totalClicks,
        totalCalls,
        conversionRate,
        avgDailyViews,
        topDays
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch analytics data",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const getVisibilityBadge = (tier: string) => {
    const variants = {
      free: "secondary",
      premium: "default",
      sponsored: "destructive"
    } as const;
    
    return (
      <Badge variant={variants[tier as keyof typeof variants] || "secondary"}>
        {tier.toUpperCase()}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-secondary/20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-secondary/20 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-4 mb-8">
          <Button variant="outline" onClick={() => navigate('/admin')}>
            <ArrowLeft className="w-4 h-4" />
          </Button>
          <div className="flex-1">
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold text-gray-900">
                {storeInfo?.name} Analytics
              </h1>
              {storeInfo && getVisibilityBadge(storeInfo.visibility_tier)}
              {storeInfo?.featured && <Badge variant="outline">Featured</Badge>}
            </div>
            <p className="text-gray-600 mt-2">Performance metrics and insights</p>
          </div>
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Views</CardTitle>
              <Eye className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalViews}</div>
              <p className="text-xs text-muted-foreground">
                {summary.avgDailyViews.toFixed(1)} per day avg
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Clicks</CardTitle>
              <MousePointer className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalClicks}</div>
              <p className="text-xs text-muted-foreground">
                Store detail views
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Phone Calls</CardTitle>
              <Phone className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalCalls}</div>
              <p className="text-xs text-muted-foreground">
                Direct calls initiated
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.conversionRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                Views to actions
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Analytics */}
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="daily">Daily Breakdown</TabsTrigger>
            <TabsTrigger value="events">Event Log</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Top Performing Days</CardTitle>
                  <CardDescription>Days with highest view counts</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {summary.topDays.map((day, index) => (
                      <div key={day.date} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{index + 1}</Badge>
                          <span className="font-medium">{new Date(day.date).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Eye className="w-4 h-4 text-gray-500" />
                          <span>{day.views}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Performance Insights</CardTitle>
                  <CardDescription>Key metrics and recommendations</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <h4 className="font-medium text-blue-900">Visibility Impact</h4>
                      <p className="text-sm text-blue-700 mt-1">
                        {storeInfo?.visibility_tier === 'sponsored' 
                          ? 'Sponsored placement is driving high visibility'
                          : storeInfo?.visibility_tier === 'premium'
                          ? 'Premium listing provides good visibility'
                          : 'Consider upgrading to premium for better visibility'
                        }
                      </p>
                    </div>
                    
                    <div className="p-3 bg-green-50 rounded-lg">
                      <h4 className="font-medium text-green-900">Conversion Rate</h4>
                      <p className="text-sm text-green-700 mt-1">
                        {summary.conversionRate > 15 
                          ? 'Excellent conversion rate! Store is performing well.'
                          : summary.conversionRate > 8
                          ? 'Good conversion rate with room for improvement.'
                          : 'Low conversion rate. Consider improving store profile.'
                        }
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="daily">
            <Card>
              <CardHeader>
                <CardTitle>Daily Analytics Breakdown</CardTitle>
                <CardDescription>Day-by-day performance metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-center text-gray-500 py-8">
                  Daily charts and detailed breakdown coming soon...
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="events">
            <Card>
              <CardHeader>
                <CardTitle>Event Log</CardTitle>
                <CardDescription>Detailed log of all tracked events</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analytics.slice(0, 20).map((event, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <Badge variant="outline">{event.event_type}</Badge>
                        <span className="text-sm text-gray-600">{event.event_date}</span>
                      </div>
                      <span className="font-medium">{event.event_count} events</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
