import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { ArrowLeft, Star, MapPin, Phone, Clock, Share2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { mockStores } from "@/data/mockData";
import SimpleReviews from "@/components/SimpleReviews";
import { toast } from "@/hooks/use-toast";

export default function StoreDetails() {
  const { id } = useParams();
  const navigate = useNavigate();

  // Mock reviews state for v1 (will be replaced with API calls)
  const [reviews, setReviews] = useState([
    {
      id: "1",
      rating: 5,
      comment:
        "Excellent service! Fixed my iPhone screen in 30 minutes. Very professional.",
      repair_type: "Screen Repair",
      created_at: "2024-12-10T10:00:00Z",
    },
    {
      id: "2",
      rating: 4,
      comment:
        "Good service and fair pricing. Took a bit longer than expected but quality work.",
      repair_type: "Battery Replacement",
      created_at: "2024-12-08T14:30:00Z",
    },
    {
      id: "3",
      rating: 5,
      comment:
        "Amazing! They saved my phone from water damage. Highly recommend!",
      repair_type: "Water Damage",
      created_at: "2024-12-05T16:15:00Z",
    },
  ]);

  const store = mockStores.find((s) => s.id === id);

  if (!store) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-white to-secondary/20 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-bold mb-2">Store not found</h2>
          <Button onClick={() => navigate("/")}>Go Home</Button>
        </div>
      </div>
    );
  }

  const handleCall = () => {
    toast({
      title: "Calling store",
      description: `Calling ${store.name} at ${store.phone}`,
    });
  };

  const handleShare = () => {
    toast({
      title: "Store shared",
      description: "Store details copied to clipboard",
    });
  };

  const handleNewReview = (newReview: {
    rating: number;
    comment: string;
    repair_type: string;
  }) => {
    const review = {
      id: Date.now().toString(),
      ...newReview,
      created_at: new Date().toISOString(),
    };
    setReviews((prev) => [review, ...prev]);
  };

  const handleClaimPromotion = () => {
    toast({
      title: "Promotion claimed!",
      description: `${store.promotion?.title} - Show this message at the store`,
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-secondary/20">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white/95 backdrop-blur-sm border-b">
        <div className="container mx-auto px-4 py-3 max-w-md">
          <div className="flex items-center justify-between">
            <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <h1 className="font-semibold">Store Details</h1>
            <Button variant="ghost" size="icon" onClick={handleShare}>
              <Share2 className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 space-y-6 max-w-md">
        {/* Store Image */}
        <div className="relative">
          <img
            src={store.image}
            alt={store.name}
            className="w-full h-48 object-cover rounded-lg"
          />
          {store.featured && (
            <Badge className="absolute top-3 left-3 bg-primary text-primary-foreground">
              Featured
            </Badge>
          )}
        </div>

        {/* Store Header */}
        <div className="space-y-4">
          <div>
            <h2 className="text-2xl font-bold">{store.name}</h2>
            <div className="flex items-center space-x-4 mt-2">
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                <span className="font-medium">{store.rating}</span>
              </div>
              <div className="flex items-center space-x-1 text-muted-foreground">
                <MapPin className="w-4 h-4" />
                <span className="text-sm">{store.distance}</span>
              </div>
            </div>
          </div>

          {store.description && (
            <p className="text-muted-foreground">{store.description}</p>
          )}
        </div>

        {/* Promotion */}
        {store.promotion && (
          <Card className="p-4 bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold text-primary">
                  {store.promotion.title}
                </h3>
                <p className="text-sm text-muted-foreground">
                  Valid until {store.promotion.validUntil}
                </p>
              </div>
              <Badge className="bg-primary text-primary-foreground">
                {store.promotion.discount}
              </Badge>
            </div>
            <Button
              className="w-full mt-3"
              size="sm"
              onClick={handleClaimPromotion}
            >
              Claim Offer
            </Button>
          </Card>
        )}

        {/* Contact Info */}
        <Card className="p-4 space-y-3">
          <h3 className="font-semibold">Contact Information</h3>

          <div className="flex items-center space-x-3">
            <MapPin className="w-5 h-5 text-muted-foreground" />
            <span className="text-sm">{store.address}</span>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Phone className="w-5 h-5 text-muted-foreground" />
              <span className="text-sm">{store.phone}</span>
            </div>
            <Button size="sm" onClick={handleCall}>
              Call
            </Button>
          </div>

          <div className="flex items-center space-x-3">
            <Clock className="w-5 h-5 text-muted-foreground" />
            <span className="text-sm">{store.hours}</span>
          </div>
        </Card>

        {/* Services */}
        {store.services && (
          <Card className="p-4">
            <h3 className="font-semibold mb-3">Services Offered</h3>
            <div className="flex flex-wrap gap-2">
              {store.services.map((service, index) => (
                <Badge key={index} variant="secondary">
                  {service}
                </Badge>
              ))}
            </div>
          </Card>
        )}

        {/* Specialties */}
        <Card className="p-4">
          <h3 className="font-semibold mb-3">Specialties</h3>
          <div className="flex flex-wrap gap-2">
            {store.specialties.map((specialty, index) => (
              <Badge key={index} variant="outline">
                {specialty}
              </Badge>
            ))}
          </div>
        </Card>

        {/* Reviews */}
        <SimpleReviews
          storeSlug={store.slug || store.id}
          storeName={store.name}
          reviews={reviews}
          averageRating={store.rating}
          totalReviews={reviews.length}
          onNewReview={handleNewReview}
        />

        {/* Actions */}
        <div className="grid grid-cols-2 gap-3 pt-4">
          <Button variant="outline" onClick={() => navigate("/stores")}>
            All Stores
          </Button>
          <Button onClick={handleCall}>Contact Store</Button>
        </div>
      </div>
    </div>
  );
}
