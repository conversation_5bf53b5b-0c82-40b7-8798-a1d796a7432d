import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Phone, MessageCircle, Mail, HelpCircle, FileText, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { toast } from '@/hooks/use-toast';

export default function Support() {
  const navigate = useNavigate();

  const handleContact = (method: string) => {
    toast({
      title: `${method} support`,
      description: `Opening ${method.toLowerCase()} support channel...`,
    });
  };

  const supportOptions = [
    {
      icon: <Phone className="w-6 h-6" />,
      title: "Call Support",
      description: "Speak directly with our support team",
      action: "Call",
      color: "bg-green-100 text-green-600",
      method: "Phone"
    },
    {
      icon: <MessageCircle className="w-6 h-6" />,
      title: "Live Chat",
      description: "Get instant help through chat",
      action: "Chat",
      color: "bg-blue-100 text-blue-600",
      method: "Chat"
    },
    {
      icon: <Mail className="w-6 h-6" />,
      title: "Email Support",
      description: "Send us your questions via email",
      action: "Email",
      color: "bg-purple-100 text-purple-600",
      method: "Email"
    }
  ];

  const helpTopics = [
    {
      icon: <HelpCircle className="w-5 h-5" />,
      title: "How to track my repair?",
      description: "Learn how to check your repair status"
    },
    {
      icon: <FileText className="w-5 h-5" />,
      title: "Warranty information",
      description: "Understanding our repair warranties"
    },
    {
      icon: <Users className="w-5 h-5" />,
      title: "Store locations",
      description: "Find repair stores near you"
    },
    {
      icon: <Phone className="w-5 h-5" />,
      title: "Emergency repairs",
      description: "Urgent repair service information"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-secondary/20">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white/95 backdrop-blur-sm border-b">
        <div className="container mx-auto px-4 py-3 max-w-md">
          <div className="flex items-center justify-between">
            <Button 
              variant="ghost" 
              size="icon"
              onClick={() => navigate('/')}
            >
              <ArrowLeft className="w-5 h-5" />
            </Button>
            <h1 className="font-semibold">Support Center</h1>
            <div className="w-10" />
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-6 space-y-6 max-w-md">
        {/* Welcome */}
        <div className="text-center space-y-2">
          <h2 className="text-xl font-bold">How can we help you?</h2>
          <p className="text-muted-foreground">Choose the best way to get support</p>
        </div>

        {/* Contact Methods */}
        <div className="space-y-3">
          <h3 className="font-semibold">Contact Support</h3>
          {supportOptions.map((option, index) => (
            <Card key={index} className="p-4 cursor-pointer hover:shadow-lg transition-shadow">
              <div className="flex items-center space-x-4">
                <div className={`p-3 rounded-lg ${option.color}`}>
                  {option.icon}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium">{option.title}</h4>
                  <p className="text-sm text-muted-foreground">{option.description}</p>
                </div>
                <Button 
                  size="sm"
                  onClick={() => handleContact(option.method)}
                >
                  {option.action}
                </Button>
              </div>
            </Card>
          ))}
        </div>

        {/* Emergency Contact */}
        <Card className="p-4 bg-red-50 border-red-200">
          <div className="flex items-center space-x-3">
            <Phone className="w-5 h-5 text-red-600" />
            <div className="flex-1">
              <h4 className="font-medium text-red-800">Emergency Hotline</h4>
              <p className="text-sm text-red-600">24/7 support for urgent repairs</p>
            </div>
            <Button 
              size="sm" 
              variant="destructive"
              onClick={() => handleContact('Emergency')}
            >
              Call Now
            </Button>
          </div>
        </Card>

        {/* Help Topics */}
        <div className="space-y-3">
          <h3 className="font-semibold">Frequently Asked Questions</h3>
          <div className="space-y-2">
            {helpTopics.map((topic, index) => (
              <Card key={index} className="p-3 cursor-pointer hover:bg-muted/50 transition-colors">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-primary/10 rounded-lg text-primary">
                    {topic.icon}
                  </div>
                  <div>
                    <h4 className="font-medium text-sm">{topic.title}</h4>
                    <p className="text-xs text-muted-foreground">{topic.description}</p>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Business Hours */}
        <Card className="p-4">
          <h3 className="font-semibold mb-3">Support Hours</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Monday - Friday</span>
              <span className="font-medium">8:00 AM - 8:00 PM</span>
            </div>
            <div className="flex justify-between">
              <span>Saturday</span>
              <span className="font-medium">9:00 AM - 6:00 PM</span>
            </div>
            <div className="flex justify-between">
              <span>Sunday</span>
              <span className="font-medium">10:00 AM - 4:00 PM</span>
            </div>
            <div className="pt-2 border-t">
              <div className="flex justify-between">
                <span>Emergency Line</span>
                <span className="font-medium text-red-600">24/7</span>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}