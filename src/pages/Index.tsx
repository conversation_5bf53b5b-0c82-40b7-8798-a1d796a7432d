import { AppHeader } from "@/components/AppHeader";
import SimpleRepairTracker from "@/components/SimpleRepairTracker";
import { QuickActions } from "@/components/QuickActions";
import { FeaturedStores } from "@/components/FeaturedStores";
import { PromotionsSection } from "@/components/PromotionsSection";

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-white to-secondary/20">
      <AppHeader />

      <main className="container mx-auto px-4 py-6 space-y-8 max-w-md">
        {/* Welcome Section */}
        <div className="text-center space-y-2 animate-fade-in">
          <h2 className="text-2xl font-bold text-foreground">Welcome back!</h2>
          <p className="text-muted-foreground">
            Track repairs & discover the best stores
          </p>
        </div>

        {/* Quick Actions */}
        <section className="animate-fade-in" style={{ animationDelay: "0.1s" }}>
          <QuickActions />
        </section>

        {/* Repair Tracker */}
        <section className="animate-fade-in" style={{ animationDelay: "0.2s" }}>
          <SimpleRepairTracker />
        </section>

        {/* Featured Stores */}
        <section className="animate-fade-in" style={{ animationDelay: "0.3s" }}>
          <FeaturedStores />
        </section>

        {/* Promotions */}
        <section className="animate-fade-in" style={{ animationDelay: "0.4s" }}>
          <PromotionsSection />
        </section>

        {/* Bottom Spacing */}
        <div className="h-8"></div>
      </main>
    </div>
  );
};

export default Index;
