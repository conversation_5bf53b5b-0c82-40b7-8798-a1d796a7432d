import React, { createContext, useContext, useState, ReactNode } from 'react';
import { RepairStatus, mockRepairs, mockUserProfile, UserProfile } from '@/data/mockData';

interface AppState {
  currentRepairs: RepairStatus[];
  userProfile: UserProfile;
  searchQuery: string;
  isLoading: boolean;
}

interface AppContextType extends AppState {
  setSearchQuery: (query: string) => void;
  setIsLoading: (loading: boolean) => void;
  findRepair: (phoneNumber: string, repairCode?: string) => RepairStatus | null;
  updateRepairStatus: (repairId: string, status: RepairStatus['status']) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [state, setState] = useState<AppState>({
    currentRepairs: mockRepairs,
    userProfile: mockUserProfile,
    searchQuery: '',
    isLoading: false,
  });

  const setSearchQuery = (query: string) => {
    setState(prev => ({ ...prev, searchQuery: query }));
  };

  const setIsLoading = (loading: boolean) => {
    setState(prev => ({ ...prev, isLoading: loading }));
  };

  const findRepair = (phoneNumber: string, repairCode?: string): RepairStatus | null => {
    return state.currentRepairs.find(repair => 
      repair.phoneNumber === phoneNumber && 
      (!repairCode || repair.repairCode === repairCode)
    ) || null;
  };

  const updateRepairStatus = (repairId: string, status: RepairStatus['status']) => {
    setState(prev => ({
      ...prev,
      currentRepairs: prev.currentRepairs.map(repair =>
        repair.id === repairId ? { ...repair, status } : repair
      )
    }));
  };

  const value: AppContextType = {
    ...state,
    setSearchQuery,
    setIsLoading,
    findRepair,
    updateRepairStatus,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};